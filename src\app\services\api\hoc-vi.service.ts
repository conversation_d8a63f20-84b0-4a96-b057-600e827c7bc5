import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
import { QueryFilerModel } from '@model';
import { hocViRouter } from '@util';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class HocViService {
  constructor(private http: _HttpClient) {}

  getFilter(model: QueryFilerModel): Observable<any> {
    return this.http.post(environment.api.baseUrl + hocViRouter.getFilter, model);
  }

  delete(id: any): Observable<any> {
    return this.http.delete(environment.api.baseUrl + hocViRouter.delete + id);
  }
  create(model: any): Observable<any> {
    return this.http.post(environment.api.baseUrl + hocViRouter.create, model);
  }

  update(id: any, model: any): Observable<any> {
    return this.http.put(environment.api.baseUrl + hocViRouter.update + id, model);
  }

  getById(id: string): Observable<any> {
    return this.http.get(environment.api.baseUrl + hocViRouter.getById + id);
  }

  getCombobox(): Observable<any> {
    return this.http.get(environment.api.baseUrl + hocViRouter.getCombobox);
  }
}
