import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { log } from '@delon/util';
import { ButtonModel } from '@model';
import { cleanForm, EVENT_TYPE, FORM_TYPE } from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subscription } from 'rxjs';
import { HeDaoTaoService } from 'src/app/services/api/he-dao-tao.service';

@Component({
  selector: 'app-he-dao-tao-item',
  templateUrl: './he-dao-tao-item.component.html',
  styleUrls: ['./he-dao-tao-item.component.less']
})
export class HeDaoTaoItemComponent implements OnInit {
  @Input() type = 'add';
  @Input() item: any;
  @Input() isVisible = false;
  @Input() option: any;
  @Output() readonly eventEmmit = new EventEmitter<any>();

  form: FormGroup;

  isInfo = false;
  isEdit = false;
  isAdd = false;
  tittle = '';

  isLoading = false;

  btnSave: ButtonModel;
  btnCancel: ButtonModel;
  btnEdit: ButtonModel;

  public Editor = ClassicEditor;

  config = {
    toolbar: {
      shouldNotGroupWhenFull: true
    }
  };

  constructor(
    private fb: FormBuilder,
    private messageService: NzMessageService,
    private heApiService: HeDaoTaoService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private aclService: ACLService
  ) {
    this.btnSave = {
      title: this.i18n.fanyi('app.common.button.save'),
      visible: true,
      enable: true,
      grandAccess: true,

      click: ($event: any) => {
        this.save();
      }
    };
    this.btnCancel = {
      title: this.i18n.fanyi('app.common.button.close'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.handleCancel();
      }
    };
    this.btnEdit = {
      title: this.i18n.fanyi('app.common.button.edit'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.updateFormToEdit();
      }
    };
    this.form = this.fb.group({
      MaHe: [null, [Validators.required]],
      TenHe: [null, [Validators.required]],
      TenHeEn: [null],
      QuyChe: [0],
      TenBacDaoTao: [null, [Validators.required]],
      TenBacDaoTaoEn: [null, [Validators.required]],
      HinhThucDaoTao: [null, [Validators.required]],
      HinhThucDaoTaoEn: [null, [Validators.required]],
      SttTrinhDo: [0, [Validators.required]]
    });
  }

  onEditorReady(editor: any): void {
    log('Editor is ready to use!', editor);
  }

  handleCancel(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.CLOSE });
  }

  ngOnInit(): void {
    this.initRightOfUser();
  }

  initRightOfUser(): void {
    this.btnSave.grandAccess = this.aclService.canAbility('HE_ADD') || this.aclService.canAbility('HE_EDIT');
    this.btnEdit.grandAccess = this.aclService.canAbility('HE_EDIT');
  }

  //#region Update-form-type
  updateFormToAdd(): void {
    this.isInfo = false;
    this.isEdit = false;
    this.isAdd = true;
    this.tittle = this.i18n.fanyi('function.training-system.modal.title-add');
    this.item = {};
    this.form.get('MaHe')?.enable();
    this.form.get('TenHe')?.enable();
    this.form.get('TenHeEn')?.enable();
    this.form.get('QuyChe')?.enable();
    this.form.get('TenBacDaoTao')?.enable();
    this.form.get('HinhThucDaoTao')?.enable();
    this.form.get('HinhThucDaoTaoEn')?.enable();
    this.form.get('SttTrinhDo')?.enable();

    this.form.get('QuyChe')?.setValue(0);
    this.form.get('SttTrinhDo')?.setValue(0);
  }

  updateFormToInfo(): void {
    this.isInfo = true;
    this.isEdit = false;
    this.isAdd = false;
    this.tittle = this.i18n.fanyi('function.training-system.modal.title-info');
    this.form.get('MaHe')?.disable();
    this.form.get('TenHe')?.disable();
    this.form.get('TenHeEn')?.disable();
    this.form.get('QuyChe')?.disable();
    this.form.get('TenBacDaoTao')?.disable();
    this.form.get('TenBacDaoTaoEn')?.disable();
    this.form.get('HinhThucDaoTao')?.disable();
    this.form.get('HinhThucDaoTaoEn')?.disable();
    this.form.get('SttTrinhDo')?.disable();
  }

  updateFormToEdit(): void {
    this.isInfo = false;
    this.isEdit = true;
    this.isAdd = false;
    this.tittle = this.i18n.fanyi('function.training-system.modal.title-edit');
    this.form.get('MaHe')?.enable();
    this.form.get('TenHe')?.enable();
    this.form.get('TenHeEn')?.enable();
    this.form.get('QuyChe')?.enable();
    this.form.get('TenBacDaoTao')?.enable();
    this.form.get('TenBacDaoTaoEn')?.enable();
    this.form.get('HinhThucDaoTao')?.enable();
    this.form.get('HinhThucDaoTaoEn')?.enable();
    this.form.get('SttTrinhDo')?.enable();
  }

  resetForm(): void {
    this.form.reset();
    this.form.get('isActive')?.setValue(true);
    this.form.get('order')?.setValue(1);
    this.form.get('isHighPriority')?.setValue(false);
  }

  updateDataToForm(data: any): void {
    this.form.get('MaHe')?.setValue(data.maHe);
    this.form.get('TenHe')?.setValue(data.tenHe);
    this.form.get('TenHeEn')?.setValue(data.tenHeEn);
    this.form.get('QuyChe')?.setValue(data.quyChe);
    this.form.get('TenBacDaoTao')?.setValue(data.tenBacDaoTao);
    this.form.get('TenBacDaoTaoEn')?.setValue(data.tenBacDaoTaoEn);
    this.form.get('HinhThucDaoTao')?.setValue(data.hinhThucDaoTao);
    this.form.get('HinhThucDaoTaoEn')?.setValue(data.hinhThucDaoTaoEn);
    this.form.get('SttTrinhDo')?.setValue(data.sttTrinhDo);
  }

  //#endregion Update-form-type

  public initData(data: any, type: any = null, option: any = {}): void {
    this.resetForm();
    this.isLoading = false;
    this.item = data;
    this.type = type;
    this.option = option;

    if (this.item?.idHe) {
      this.getDataInfo(this.item.idHe);
    }
    switch (type) {
      case FORM_TYPE.ADD:
        this.updateFormToAdd();
        break;
      case FORM_TYPE.INFO:
        this.updateFormToInfo();
        break;
      case FORM_TYPE.EDIT:
        this.updateFormToEdit();
        break;
      default:
        this.updateFormToAdd();
        break;
    }
  }

  closeModalReloadData(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
  }

  getDataInfo(id: any): Subscription | undefined {
    this.isLoading = true;
    const rs = this.heApiService.getById(id).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data === null || res.data === undefined) {
          this.messageService.error(`${res.message}`);
          return;
        }
        this.item = res.data;
        this.updateDataToForm(res.data);
      },
      error: (err: any) => {
        this.isLoading = false;
      },
      complete: () => {
        this.isLoading = false;
      }
    });
    return rs;
  }

  save(): Subscription | undefined {
    this.isLoading = true;

    cleanForm(this.form);
    for (const i in this.form.controls) {
      this.form.controls[i].markAsDirty();
      this.form.controls[i].updateValueAndValidity();
    }
    if (!this.form.valid) {
      this.isLoading = false;
      this.messageService.error(this.i18n.fanyi('app.common.form.dirty'));
      return;
    }

    const data = {
      IdHe: this.item.idHe,
      MaHe: this.form.get('MaHe')?.value,
      TenHe: this.form.get('TenHe')?.value,
      TenHeEn: this.form.get('TenHeEn')?.value,
      QuyChe: this.form.get('QuyChe')?.value,
      TenBacDaoTao: this.form.get('TenBacDaoTao')?.value,
      TenBacDaoTaoEn: this.form.get('TenBacDaoTaoEn')?.value,
      HinhThucDaoTao: this.form.get('HinhThucDaoTao')?.value,
      HinhThucDaoTaoEn: this.form.get('HinhThucDaoTaoEn')?.value,
      SttTrinhDo: this.form.get('SttTrinhDo')?.value
    };

    if (this.isAdd) {
      const promise = this.heApiService.create(data).subscribe({
        next: (res: any) => {
          this.isLoading = false;
          if (res.data === null || res.data === undefined) {
            this.messageService.error(`${res.message}`);
            return;
          }

          this.messageService.success(`${res.message}`);
          this.closeModalReloadData();
        },
        error: (err: any) => {
          this.isLoading = false;
          if (err.error) {
            this.messageService.error(`${err.error.message}`);
          } else {
            this.messageService.error(`${err.status}`);
          }
        },
        complete: () => (this.isLoading = false)
      });
      return promise;
    } else if (this.isEdit) {
      const promise = this.heApiService.update(this.item.idHe, data).subscribe({
        next: (res: any) => {
          this.isLoading = false;
          if (res.data === null || res.data === undefined) {
            this.messageService.error(`${res.message}`);
            return;
          }
          this.messageService.success(`${res.message}`);
          this.closeModalReloadData();
        },
        error: (err: any) => {
          this.isLoading = false;
          if (err.error) {
            this.messageService.error(`${err.error.message}`);
          } else {
            this.messageService.error(`${err.status}`);
          }
        },
        complete: () => (this.isLoading = false)
      });
      return promise;
    } else {
      return;
    }
  }
}
