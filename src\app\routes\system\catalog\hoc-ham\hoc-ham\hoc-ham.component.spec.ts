import { ComponentFixture, TestBed } from '@angular/core/testing';

import { Ho<PERSON><PERSON>amComponent } from './hoc-ham.component';

describe('HocHamComponent', () => {
  let component: HocHamComponent;
  let fixture: ComponentFixture<HocHamComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [HocHamComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(HocHamComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
