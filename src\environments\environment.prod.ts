// This file can be replaced during build by using the `fileReplacements` array.
// `ng build ---prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

import { Environment } from '@delon/theme';

export const environment = {
  production: true,
  build: '1752351061',
  version: '1.1.0',
  useHash: false,
  phanHe: 'UniSystem',
  serverUrl: `./`,
  authType: '', // authorizeType support list: sso, hou, jwt, keycloak
  vaultToken: '',
  identiyServer: {
    baseUrl: '',
    clientId: ''
  },
  houCasServer: {
    serverUrl: ''
  },
  keycloakServer: {
    baseUrl: '',
    realm: '',
    clientId: '',
    scopes: 'openid profile email'
  },
  api: {
    baseUrl: '',
    refreshTokenEnabled: true,
    refreshTokenType: 'auth-refresh'
  },
  pro: {
    theme: 'light',
    menu: 'side',
    contentWidth: 'fluid',
    fixedHeader: false,
    autoHideHeader: false,
    fixSiderbar: true,
    onlyIcon: false
  }
} as Environment;

/*
 * In development mode, to ignore zone related error stack frames such as
 * `zone.run`, `zoneDelegate.invokeTask` for easier debugging, you can
 * import the following file, but please comment it out in production mode
 * because it will have performance impact when throw error
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
