<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="modalTitle"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  nzMaskClosable="false"
  nzWidth="1000px"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalTitle> {{ tittle }} </ng-template>

  <ng-template #modalContent>
    <form nz-form [formGroup]="form" (ngSubmit)="save()">
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="MaHocVi">{{ 'function.degree.modal.form.code' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.degree.modal.form.code.required' | i18n }}">
          <input
            nz-input
            formControlName="MaHocVi"
            id="MaHocVi"
            placeholder="{{ 'function.degree.modal.form.code.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="HocVi">{{ 'function.degree.modal.form.name' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.degree.modal.form.name.required' | i18n }}">
          <input nz-input formControlName="HocVi" id="HocVi" placeholder="{{ 'function.degree.modal.form.name.place-holder' | i18n }}" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="HeSoBuKhoaHoc">
          {{ 'function.degree.modal.form.course-compensation-coefficient' | i18n }}
        </nz-form-label>
        <nz-form-control
          [nzSm]="17"
          [nzXs]="24"
          nzErrorTip="{{ 'function.degree.modal.form.course-compensation-coefficient.required' | i18n }}"
        >
          <input
            nz-input
            formControlName="HeSoBuKhoaHoc"
            id="HeSoBuKhoaHoc"
            type="number"
            step="0.01"
            placeholder="{{ 'function.degree.modal.form.course-compensation-coefficient.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-template>

  <ng-template #modalFooter>
    <button
      nz-button
      nzType="primary"
      class="btn-secondary"
      *ngIf="isInfo && btnEdit.visible && btnEdit.grandAccess"
      (click)="btnEdit.click($event)"
    >
      <i nz-icon nzType="edit" nzTheme="fill"></i>{{ btnEdit.title }}
    </button>
    <button
      nz-button
      nzType="primary"
      class="btn-primary"
      *ngIf="!isInfo && btnSave.visible && btnSave.grandAccess"
      [nzLoading]="isLoading"
      (click)="btnSave.click($event)"
    >
      <i nz-icon nzType="save" nzTheme="fill"></i>{{ btnSave.title }}
    </button>
    <button
      nz-button
      nzType="default"
      class="btn-warning"
      *ngIf="btnCancel.visible && btnCancel.grandAccess"
      (click)="btnCancel.click($event)"
    >
      <i nz-icon nzType="close-circle" nzTheme="fill"></i>{{ btnCancel.title }}
    </button>
  </ng-template>
</nz-modal>
