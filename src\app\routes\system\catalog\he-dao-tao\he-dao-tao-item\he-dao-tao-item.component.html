<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="modalTitle"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  nzMaskClosable="false"
  nzWidth="1000px"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalTitle> {{ tittle }} </ng-template>

  <ng-template #modalContent>
    <form nz-form [formGroup]="form" (ngSubmit)="save()">
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="MaHe">{{
          'function.training-system.modal.form.code' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.training-system.modal.form.code.required' | i18n }}">
          <input
            nz-input
            formControlName="MaHe"
            id="MaHe"
            placeholder="{{ 'function.training-system.modal.form.code.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="TenHe">{{
          'function.training-system.modal.form.name' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.training-system.modal.form.name.required' | i18n }}">
          <input
            nz-input
            formControlName="TenHe"
            id="TenHe"
            placeholder="{{ 'function.training-system.modal.form.name.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="TenHeEn">{{
          'function.training-system.modal.form.name-En' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <input
            nz-input
            formControlName="TenHeEn"
            id="TenHeEn"
            placeholder="{{ 'function.training-system.modal.form.name-En.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="QuyChe">{{
          'function.training-system.modal.form.regulation' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <input
            nz-input
            formControlName="QuyChe"
            id="QuyChe"
            type="number"
            placeholder="{{ 'function.training-system.modal.form.regulation.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="TenBacDaoTao">{{
          'function.training-system.modal.form.education-level-name' | i18n
        }}</nz-form-label>
        <nz-form-control
          [nzSm]="17"
          [nzXs]="24"
          nzErrorTip="{{ 'function.training-system.modal.form.education-level-name.required' | i18n }}"
        >
          <input
            nz-input
            formControlName="TenBacDaoTao"
            id="TenBacDaoTao"
            placeholder="{{ 'function.training-system.modal.form.education-level-name.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="TenBacDaoTaoEn">{{
          'function.training-system.modal.form.education-level-name-En' | i18n
        }}</nz-form-label>
        <nz-form-control
          [nzSm]="17"
          [nzXs]="24"
          nzErrorTip="{{ 'function.training-system.modal.form.education-level-name-En.required' | i18n }}"
        >
          <input
            nz-input
            formControlName="TenBacDaoTaoEn"
            id="TenBacDaoTaoEn"
            placeholder="{{ 'function.training-system.modal.form.education-level-name-En.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" nzRequired [nzXs]="24" nzFor="HinhThucDaoTao">{{
          'function.training-system.modal.form.form-of-training' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.training-system.modal.form.form-of-training.required' | i18n }}">
          <input
            nz-input
            formControlName="HinhThucDaoTao"
            id="HinhThucDaoTao"
            placeholder="{{ 'function.training-system.modal.form.form-of-training.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="HinhThucDaoTaoEn">{{
          'function.training-system.modal.form.form-of-training-En' | i18n
        }}</nz-form-label>
        <nz-form-control
          [nzSm]="17"
          [nzXs]="24"
          nzErrorTip="{{ 'function.training-system.modal.form.form-of-training-En.required' | i18n }}"
        >
          <input
            nz-input
            formControlName="HinhThucDaoTaoEn"
            id="HinhThucDaoTaoEn"
            placeholder="{{ 'function.training-system.modal.form.form-of-training-En.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="SttTrinhDo">{{
          'function.training-system.modal.form.degree-number' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.training-system.modal.form.degree-number.required' | i18n }}">
          <input
            nz-input
            formControlName="SttTrinhDo"
            id="SttTrinhDo"
            type="number"
            placeholder="{{ 'function.training-system.modal.form.degree-number.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-template>

  <ng-template #modalFooter>
    <button
      nz-button
      nzType="primary"
      class="btn-secondary"
      *ngIf="isInfo && btnEdit.visible && btnEdit.grandAccess"
      (click)="btnEdit.click($event)"
    >
      <i nz-icon nzType="edit" nzTheme="fill"></i>{{ btnEdit.title }}
    </button>
    <button
      nz-button
      nzType="primary"
      class="btn-primary"
      *ngIf="!isInfo && btnSave.visible && btnSave.grandAccess"
      [nzLoading]="isLoading"
      (click)="btnSave.click($event)"
    >
      <i nz-icon nzType="save" nzTheme="fill"></i>{{ btnSave.title }}
    </button>
    <button
      nz-button
      nzType="default"
      class="btn-warning"
      *ngIf="btnCancel.visible && btnCancel.grandAccess"
      (click)="btnCancel.click($event)"
    >
      <i nz-icon nzType="close-circle" nzTheme="fill"></i>{{ btnCancel.title }}
    </button>
  </ng-template>
</nz-modal>
