{"app.common.message.new-version-detect.title": "Đã có phiên bản mới!", "app.common.message.new-version-detect.content": "<PERSON><PERSON> lòng nhấn \"<PERSON><PERSON><PERSON> lại\" để cập nhật phiên bản mới nhất.", "app.common.message.new-version-detect.button-cancel": "Đ<PERSON><PERSON>", "app.common.message.new-version-detect.button": "<PERSON><PERSON><PERSON> l<PERSON>i", "footer-copyright": "Copyright", "footer-name": "<PERSON><PERSON><PERSON><PERSON>", "footer-url": "http://thienan.vn", "footer-login-passport-text": "<PERSON><PERSON><PERSON> quản trị hệ thống - <PERSON><PERSON><PERSON> triển bởi công ty công nghệ", "footer-login-passport-text-url": "<PERSON><PERSON><PERSON><PERSON>", "footer-login-passport-url": "http://thienan.vn/", "form.login": "<PERSON><PERSON><PERSON>", "form.login.title.authentic": "<PERSON><PERSON><PERSON> th<PERSON>c ng<PERSON> dùng", "form.login.title": "Uni System", "form.login.desc": "<PERSON><PERSON><PERSON> thông tin quản trị hệ thống", "passport.login.username.placeholder": "<PERSON><PERSON><PERSON><PERSON> tên đăng nhập", "app.login.remember-me": "<PERSON><PERSON> trì đăng nh<PERSON>p", "app.login.sign-in-with": "Bạn chưa có tài k<PERSON>n?", "app.login.forgot-password": "<PERSON>uên mật khẩu?", "app.login.signup": "<PERSON><PERSON><PERSON> ký ngay", "app.login.login": "<PERSON><PERSON><PERSON>", "app.login.tab-login-credentials": "<PERSON><PERSON><PERSON>", "identity card number.required": "<PERSON><PERSON> lòng nhập số CMND/CCCD", "identity card number.placeholder": "Nhập số CMND/CCCD", "password.required": "<PERSON><PERSON><PERSON> kh<PERSON>u tối thiểu 6 ký tự", "old-password.placeholder": "<PERSON><PERSON><PERSON><PERSON> hiện tại", "new-password.placeholder": "<PERSON><PERSON><PERSON> mới", "confirm-new-password.placeholder": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu mới", "password.placeholder": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u", "password-login.required": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u", "form.forgot-password.label": "<PERSON>uên mật khẩu?", "password-confirm.placeholder": "<PERSON><PERSON><PERSON> nhận lại mật kh<PERSON>u", "form.login.label": "<PERSON><PERSON><PERSON>", "form.login.placeholder": "<PERSON><PERSON><PERSON><PERSON>", "form.login.required": "<PERSON><PERSON> lòng nh<PERSON>p tà<PERSON>", "form.password.label": "<PERSON><PERSON><PERSON>", "app.login.tab-login-forgot-password": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "app.login.return-to-page": "Quay lại trang", "app.login.return-to-the-login-page": "<PERSON><PERSON><PERSON>", "app.login.send require": "GỬI YÊU CẦU", "email.placeholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ Email", "validation.email.required": "<PERSON><PERSON> lòng nhập địa chỉ email", "validation.email.wrong-format": "<PERSON><PERSON> bị sai định dạng", "app.forgot-password.forgot-password": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "app.passport.recover-password": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> mật kh<PERSON>u", "layout.grid.search.status.label": "<PERSON><PERSON><PERSON><PERSON> thái", "layout.grid.search.status.placeholder": "<PERSON><PERSON><PERSON> trạng thái", "layout.grid.search.text-search.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "layout.grid.action": "<PERSON><PERSON>", "layout.grid.index": "STT", "layout.button.btn-reload.label": "<PERSON><PERSON><PERSON> l<PERSON>i", "layout.button.btn-reset.label": "Đặt lại", "layout.button.btn-upload.label": "<PERSON><PERSON><PERSON>", "layout.button.btn-upload-excel.label": "<PERSON><PERSON><PERSON> lên dữ liệu", "layout.button.btn-download-excel.label": "<PERSON><PERSON><PERSON> về mẫu", "layout.button.btn-add.label": "<PERSON><PERSON><PERSON><PERSON> mới", "layout.button.btn-cancel.label": "Đ<PERSON><PERSON>", "layout.button.btn-delete.label": "Xóa", "layout.button.btn-save.label": "<PERSON><PERSON><PERSON>", "layout.button.btn-saveandcreate.label": "Lư<PERSON> & <PERSON><PERSON>ê<PERSON> mới", "layout.button.btn-edit.label": "<PERSON><PERSON><PERSON>", "layout.button.btn-info.label": "<PERSON> ti<PERSON>", "layout.button.btn-active.label": "Mở khóa", "layout.button.btn-deactive.label": "Khóa", "layout.button.btn-pie-chart.label": "Báo cáo", "layout.button.btn-setting.label": "<PERSON><PERSON><PERSON> hình thông tin kết nối", "layout.button.btn-search.label": "<PERSON><PERSON><PERSON>", "layout.button.btn-excel.label": "Excel", "layout.button.btn-exportexcel.label": "Export excel", "layout.button.btn-importexcel.label": "Import excel", "layout-modal.delete.message.many": "Bạn có chắc chắn muốn xóa các bản ghi này không?", "layout-modal.delete.message.one": "Bạn có chắc chắn muốn xóa bản ghi này không?", "delete-modal.grid.header": "<PERSON><PERSON><PERSON>n x<PERSON>a", "delete-modal.grid.name": "<PERSON><PERSON><PERSON>", "delete-modal.grid.result": "<PERSON><PERSON><PERSON><PERSON> thái", "delete-modal.grid.message": "<PERSON><PERSON>", "delete-modal.grid.button.btn-delete": "Đồng ý", "menu.fullscreen": "<PERSON><PERSON><PERSON> màn hình", "menu.fullscreen.exit": "<PERSON><PERSON><PERSON><PERSON> toàn màn hình", "menu.clear.local.storage": "<PERSON><PERSON><PERSON> bộ nhớ đệm", "menu.lang": "<PERSON><PERSON><PERSON>", "menu.main": "<PERSON><PERSON>", "menu.dashboard": "Trang chủ", "menu.profile": "Thông tin cá nhân", "menu.account": "<PERSON><PERSON><PERSON>", "menu.account.center": "Thông tin cá nhân", "menu.account.settings": "Cài đặt tài k<PERSON>n", "menu.account.reset-cache": "Xóa cache", "menu.account.change-password": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "menu.account.logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "menu.report": "Báo cáo", "menu.setting": "<PERSON><PERSON><PERSON> h<PERSON>nh", "menu.extras.helpcenter": "<PERSON><PERSON><PERSON> g<PERSON>", "menu.extras.settings": "Cài đặt", "message.remove-cache-success": "<PERSON>óa cache thành công!", "button.title.detail": "<PERSON>em chi tiết", "button.upload.title": "<PERSON><PERSON>n file để uplaod", "layout.grid.pagination.shows": "<PERSON><PERSON><PERSON> thị", "layout.grid.pagination.total": "trên tổng s<PERSON>", "layout.grid.pagination.result": "kế<PERSON> quả", "common.list": "<PERSON><PERSON>", "common.add": "<PERSON><PERSON><PERSON><PERSON> mới", "common.edit": "<PERSON><PERSON><PERSON>", "common.delete": "Xóa", "common.invalid": "<PERSON><PERSON>n cần nhập đủ các trường thông tin bắt buộc!", "common.require.message": "<PERSON><PERSON><PERSON> là trường b<PERSON>t buộ<PERSON>!", "common.update.title.success": "<PERSON><PERSON><PERSON> nhật thành công!", "common.update.title.fail": "<PERSON><PERSON><PERSON> nhật không thành công. <PERSON>ui lòng thử lại!", "common.delete.confirm": "<PERSON><PERSON><PERSON>n x<PERSON>a", "common.delete.content": "Bạn có chắc chắn muốn xóa bản ghi này không?", "common.deletes.content": "Bạn có chắc chắn muốn xóa các bản ghi này không?", "common.delete.warning": "<PERSON><PERSON><PERSON> chọn bản ghi muốn xóa!", "common.no-data": "<PERSON><PERSON><PERSON>ng có thông tin", "common.captcha-btn.second": "s", "common.captcha-btn.send": "Send", "common.form.invalid-data": "<PERSON><PERSON>m tra lại thông tin các trường đã nhập!", "common.import.invalid-data": "<PERSON><PERSON><PERSON> lên dữ liệu thất bại. <PERSON><PERSON><PERSON> tra lại thông tin các trường đã nhập!", "app.common.table.grid-index": "STT", "app.common.table.grid-action": "<PERSON><PERSON>", "app.common.button-change-pass.save": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "app.common.button.save": "<PERSON><PERSON><PERSON>", "app.common.button.add": "<PERSON><PERSON><PERSON><PERSON> mới", "app.common.button.edit": "<PERSON><PERSON><PERSON>", "app.common.button.delete": "Xóa", "app.common.button.close": "Đ<PERSON><PERSON>", "app.common.button.import-excel": "Import excel", "app.common.button.export-excel": "Export excel", "app.common.button.search": "<PERSON><PERSON><PERSON>", "app.common.button.reset": "Đặt lại", "app.common.button.refresh": "<PERSON><PERSON><PERSON> l<PERSON>i", "app.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "app.confirm-delete.content": "Bạn có chắc chắn muốn xóa nội dung này", "app.common.error.title": "<PERSON><PERSON> lỗi xảy ra", "app.common.error.content": "<PERSON><PERSON> lòng liên hệ quản trị hệ thống để biết xử lý.", "app.common.form.dirty": "<PERSON><PERSON>m tra lại thông tin các trường đã nhập!", "app.common.delete-success": "<PERSON><PERSON>a dữ liệu thành công!", "oidc-callback.accout-deactive": "Tài khoản của bạn đang ở trạng thái khóa hoặc hoặc chưa được kích hoạt", "oidc-callback.accout-description": "<PERSON><PERSON> lòng liên hệ với quản trị viên để được hỗ trợ", "oidc-callback.logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "oidc-callback.try-again": "<PERSON><PERSON><PERSON> lại", "validation.phone-number": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "validation.oidc-callback-code": "<PERSON><PERSON> xác thực", "validation.password": "<PERSON><PERSON><PERSON>", "layout.modal.header.add": "<PERSON><PERSON><PERSON><PERSON> mới", "layout.modal.header.edit": "<PERSON><PERSON><PERSON>", "layout.modal.header.info": "<PERSON> ti<PERSON>", "layout.input.search.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "layout.btn.save.label": "<PERSON><PERSON><PERSON>", "layout.btn.cancel.label": "Hủy bỏ", "layout.btn.close.label": "Đ<PERSON><PERSON>", "table.action.title": "<PERSON><PERSON><PERSON> đ<PERSON>", "table.nodata.title": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "combobox.empty": "<PERSON><PERSON><PERSON><PERSON>", "datepicker.place_holder": "<PERSON><PERSON><PERSON>", "datepicker.month.place_holder": "<PERSON><PERSON><PERSON>g", "datepicker.year.place_holder": "<PERSON><PERSON><PERSON>", "app.confirm-delete.ok-text": "Xóa", "app.confirm-delete.cancel-text": "Hủy bỏ", "app.no-data-selected-to-delete": "<PERSON><PERSON><PERSON> chọn dữ liệu để xóa", "message.error-500": "Có lỗi xảy ra:", "function.email-template.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.email-template.table.code": "Mã", "function.email-template.table.name": "<PERSON><PERSON><PERSON>", "function.email-template.table.active": "<PERSON>r<PERSON><PERSON> thái sử dụng", "function.email-template.table.order": "<PERSON><PERSON><PERSON> tự hiển thị", "function.email-template.table.description": "<PERSON><PERSON>", "function.email-template.table.ky-hieu-cham-cong": "<PERSON><PERSON> hi<PERSON>u chấm công", "function.email-template.page.title": "<PERSON><PERSON> s<PERSON>ch mẫu email", "function.email-template.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.email-template.confirm-delete.content": "Bạn có chắc chắn muốn xóa mẫu email:", "function.email-template.modal.form.code": "Mã", "function.email-template.modal.form.code.required": "<PERSON><PERSON> không đư<PERSON><PERSON> để trống", "function.email-template.modal.form.code.place-holder": "Mã mẫu email", "function.email-template.modal.form.name": "<PERSON><PERSON><PERSON>", "function.email-template.modal.form.name.required": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "function.email-template.modal.form.name.place-holder": "Tên mẫu email", "function.email-template.modal.form.subject": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "function.email-template.modal.form.subject.required": "Ti<PERSON><PERSON> đề email không đư<PERSON><PERSON> để trống", "function.email-template.modal.form.subject.place-holder": "Ti<PERSON><PERSON> đ<PERSON> email", "function.email-template.modal.form.template": "Mẫu email", "function.email-template.modal.form.template.required": "Mẫu email không đư<PERSON><PERSON> để trống", "function.email-template.modal.form.template.place-holder": "Mẫu email", "function.email-template.modal.form.cc": "CC", "function.email-template.modal.form.cc.place-holder": "CC to emails", "function.email-template.modal.form.bcc": "BCC", "function.email-template.modal.form.bcc.place-holder": "BCC to emails", "function.email-template.modal.form.fromEmail": "From email", "function.email-template.modal.form.fromEmail.place-holder": "<PERSON><PERSON><PERSON><PERSON> từ email", "function.email-template.modal.form.fromUser": "From user", "function.email-template.modal.form.fromUser.place-holder": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i từ User", "function.email-template.modal.form.isHighPriority": "<PERSON><PERSON><PERSON> dấu là email ưu tiên", "function.email-template.modal.form.isActive": "Sử dụng", "function.email-template.modal.form.order": "<PERSON><PERSON><PERSON> tự hiển thị", "function.email-template.modal.form.description": "<PERSON><PERSON>", "function.email-template.modal.form.description.place-holder": "<PERSON><PERSON>i dung mô tả", "function.email-template.modal.title-add": "Thê<PERSON> mới mẫu email", "function.email-template.modal.title-edit": "<PERSON><PERSON><PERSON> nhật mẫu email", "function.email-template.modal.title-info": "<PERSON> tiết mẫu email", "function.workflow.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.workflow.confirm-delete.content": "Bạn có chắc chắn muốn xóa quy trình này", "function.workflow.modal.title-add": "<PERSON>hêm quy trình", "function.workflow.modal.title-info": "<PERSON>em chi tiết quy trình", "function.workflow.modal.title-edit": "<PERSON><PERSON><PERSON> quy trình", "function.workflow.page.title": "<PERSON><PERSON> s<PERSON>ch quy trình", "function.system-log.title": "<PERSON><PERSON> s<PERSON>ch nh<PERSON>t ký hệ thống", "function.system-log.header": "<PERSON><PERSON><PERSON><PERSON> ký hệ thống", "function.system-log.grid.organizationid.label": "Đơn vị", "function.system-log.grid.organizationid.placeholder": "<PERSON><PERSON>n đơn vị", "function.system-log.grid.userid.label": "<PERSON><PERSON><PERSON><PERSON> dùng", "function.system-log.grid.userid.placeholder": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> dùng", "function.system-log.grid.device.label": "<PERSON><PERSON><PERSON><PERSON> bị", "function.system-log.grid.device.placeholder": "<PERSON><PERSON><PERSON> th<PERSON> bị", "function.system-log.grid.createddate": "<PERSON><PERSON><PERSON>", "function.system-log.grid.actionname": "<PERSON><PERSON><PERSON> đ<PERSON>", "function.system-log.grid.clientInfo": "<PERSON><PERSON><PERSON><PERSON> bị", "function.system-log.grid.correlationId": "Correlation Id", "function.system-log.grid.description": "<PERSON><PERSON>", "function.system-log.grid.traceid": "Trace Id", "function.system-log.grid.username": "<PERSON><PERSON><PERSON><PERSON> dùng", "function.system-log.grid.organizationname": "Đơn vị", "function.system-log.grid.device": "<PERSON><PERSON><PERSON><PERSON> bị", "function.system-log.grid.ip": "<PERSON> thiết bị", "function.system-log.grid.timeExecution": "<PERSON><PERSON><PERSON><PERSON> gian th<PERSON> hi<PERSON>n", "function.system-log.grid.created-date.label": "<PERSON><PERSON><PERSON>", "function.system-log.grid.action-code.label": "<PERSON><PERSON><PERSON> đ<PERSON>", "function.system-log.grid.action-code.placeholder": "<PERSON><PERSON><PERSON> hành động", "function.role.title": "<PERSON><PERSON> mục nhóm người dùng", "function.role.grid.code": "Mã nhóm", "function.role.grid.name": "<PERSON><PERSON><PERSON>", "function.role.grid.description": "<PERSON><PERSON>", "function.role.grid.status": "<PERSON><PERSON><PERSON><PERSON> thái", "function.role.modal-item.header-add": "<PERSON>h<PERSON><PERSON> mới nhóm người dùng", "function.role.modal-item.header-info": "<PERSON> tiết nhóm người dùng", "function.role.modal-item.header-edit": "<PERSON><PERSON><PERSON> nh<PERSON>t nhóm người dùng", "function.role.modal-item.error.message.form-invalid": "<PERSON><PERSON>m tra lại thông tin các trường đã nhập!", "function.role.modal-item.error.message.name": "Tên nhóm người dùng không được để trống!", "function.role.modal-item.code.label": "Mã nhóm người dùng", "function.role.modal-item.code.placeholder": "<PERSON><PERSON><PERSON><PERSON> mã nhóm người dùng", "function.role.modal-item.name.label": "<PERSON><PERSON><PERSON> n<PERSON> người dùng", "function.role.modal-item.name.placeholder": "<PERSON><PERSON><PERSON><PERSON> tên nhóm người dùng", "function.role.modal-item.status.label": "<PERSON><PERSON><PERSON><PERSON> thái", "function.role.modal-item.status-true.label": "<PERSON><PERSON>", "function.role.modal-item.status-false.label": "<PERSON><PERSON><PERSON> dụng", "function.role.modal-item.description.label": "<PERSON><PERSON><PERSON>", "function.role.modal-item.description.placeholder": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "function.role.modal-item.code.message.maxlength": "Tối đa 70 ký tự!", "function.role.modal-item.code.message.minlength": "T<PERSON>i thiểu 1 ký tự!", "function.role.modal-item.code.message.required": "Trường thông tin bắt buộc!", "function.role.modal-item.code.message.pattern": "<PERSON>h<PERSON>ng đúng đinh dạng!", "function.role.modal-item.name.message.maxlength": "Tối đa 70 ký tự!", "function.role.modal-item.name.message.minlength": "T<PERSON>i thiểu 1 ký tự!", "function.role.modal-item.name.message.required": "Trường thông tin bắt buộc!", "function.role.modal-item.name.message.pattern": "<PERSON>h<PERSON>ng đúng đinh dạng!", "function.role.modal-item.name.message.error.title": "<PERSON><PERSON> lỗi xảy ra", "function.role.modal-permission.header": "<PERSON><PERSON> quyền chức năng nhóm người dùng", "function.role.modal-permission.document": "<PERSON><PERSON> quyền dữ liệu tài liệu", "function.role.modal-permission.document.organization.label": "<PERSON><PERSON> quyền theo đơn vị", "function.role.modal-permission.document.organization.placeholder": "<PERSON><PERSON>n đơn vị", "function.role.modal-permission.user": "<PERSON><PERSON> quyền dữ liệu kh<PERSON>ch hàng", "function.role.modal-permission.user.organization.label": "<PERSON><PERSON> quyền theo đơn vị", "function.role.modal-permission.user.organization.placeholder": "<PERSON><PERSON>n đơn vị", "function.role.modal-user-role.header": "<PERSON><PERSON> quyền theo người dùng - nhóm người dùng:", "function.role.modal-user-role.organization.label": "Đơn vị", "function.role.modal-user-role.organization.placeholder": "<PERSON><PERSON>n đơn vị", "function.role.modal-user-role.table.header.name": "<PERSON><PERSON><PERSON> dùng", "function.role.modal-user-role.table.header.code": "<PERSON><PERSON><PERSON> đ<PERSON>p", "function.role.grid.button.add-right": "<PERSON><PERSON> quyền theo chức năng", "function.role.modal-right.header": "<PERSON><PERSON> quyền chức năng", "function.role.modal-permission.search.label": "<PERSON><PERSON><PERSON>", "function.role.modal-api.header": "<PERSON><PERSON> quyền api", "function.role.modal-api.search.label": "<PERSON><PERSON><PERSON>", "function.role.grid.button.add-menu": "<PERSON><PERSON> quyền <PERSON>", "function.role.modal-navigation.header": "<PERSON><PERSON><PERSON> quy<PERSON>", "function.role.modal-menu.header": "<PERSON><PERSON> quyền <PERSON>", "function.role.modal-menu.search.label": "<PERSON><PERSON><PERSON>", "function.user.title": "<PERSON><PERSON> s<PERSON>ch ng<PERSON><PERSON> dùng", "function.user.header": "<PERSON><PERSON> s<PERSON>ch ng<PERSON><PERSON> dùng", "function.user.grid.username": "<PERSON><PERSON><PERSON> tà<PERSON>", "function.user.grid.name": "<PERSON><PERSON> tên", "function.user.grid.statusname": "<PERSON><PERSON><PERSON><PERSON> thái", "function.user.grid.document-code": "<PERSON><PERSON><PERSON> l<PERSON>", "function.user.grid.maCanBoUser": "<PERSON><PERSON> cán bộ", "function.user.grid.email": "Email", "function.user.grid.organizationid.label": "Đơn vị", "function.user.grid.organizationid.placeholder": "<PERSON><PERSON><PERSON> đơn vị", "function.user.modal-item.header-add": "<PERSON><PERSON><PERSON><PERSON> mới người dùng", "function.user.modal-item.header-edit": "<PERSON><PERSON><PERSON> nh<PERSON>t ng<PERSON><PERSON> dùng", "function.user.modal-item.organization.label": "Đơn vị", "function.user.modal-item.search.organization.placeholder": "<PERSON><PERSON>n đơn vị", "function.user.modal-item.search.organization.error-message.required": "Đơn vị chưa đ<PERSON><PERSON><PERSON> chọn!", "function.user.modal-item.name.label": "Họ và tên", "function.user.modal-item.name.placeholder": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>o họ tên", "function.user.modal-item.name.error-message.required": "Họ tên không được để trống!", "function.user.modal-item.birthday.label": "<PERSON><PERSON><PERSON>", "function.user.modal-item.search.birthday.placeholder": "<PERSON><PERSON><PERSON> ng<PERSON> sinh", "function.user.modal-item.sex.label": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "function.user.modal-item.phonenumber.label": "SĐT", "function.user.modal-item.phonenumber.placeholder": "<PERSON><PERSON><PERSON><PERSON> vào số điện thoại", "function.user.modal-item.phonenumber.error-message.required": "<PERSON><PERSON> điện thoại không được để trống!", "function.user.modal-item.phonenumber.error-message.wrong-format": "<PERSON><PERSON><PERSON><PERSON> đúng định dạng số điện thoại", "function.user.modal-item.email.label": "Email", "function.user.modal-item.email.placeholder": "Nhập vào email", "function.user.modal-item.email.error-message.required": "Email không đư<PERSON><PERSON> để trống!", "function.user.modal-item.email.error-message.wrong-format": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>g đ<PERSON>nh dạng email", "function.user.modal-item.provincename.label": "Tỉnh/TP", "function.user.modal-item.provincename.placeholder": "<PERSON><PERSON><PERSON><PERSON> vào Tỉnh/Thành phố", "function.user.modal-item.districtname.label": "Quận/huyện", "function.user.modal-item.districtname.placeholder": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>/huy<PERSON>n", "function.user.modal-item.issuedate.label": "<PERSON><PERSON><PERSON> c<PERSON>", "function.user.modal-item.search.issuedate.placeholder": "<PERSON><PERSON><PERSON> ng<PERSON> c<PERSON>p", "function.user.modal-item.position.label": "<PERSON><PERSON><PERSON> v<PERSON>", "function.user.modal-item.position.placeholder": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> vụ", "function.user.modal-item.issueby.label": "<PERSON><PERSON><PERSON> c<PERSON>p", "function.user.modal-item.issueby.placeholder": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> n<PERSON> cấp", "function.user.modal-item.countryname.label": "Quốc gia", "function.user.modal-item.countryname.placeholder": "<PERSON><PERSON><PERSON><PERSON> vào quốc gia", "function.user.modal-item.address.label": "Địa chỉ", "function.user.modal-item.address.placeholder": "<PERSON><PERSON><PERSON><PERSON> vào địa chỉ", "function.user.modal-item.user-name.label": "<PERSON><PERSON><PERSON> đ<PERSON>p", "function.user.modal-item.user-name.placeholder": "<PERSON><PERSON><PERSON><PERSON> vào tên đăng nhập", "function.user.modal-item.user-name.error-message.required": "Tên tài khoản không được để trống!", "function.user.modal-item.status.label": "<PERSON><PERSON><PERSON><PERSON> thái", "function.user.modal-item.status-true.label": "<PERSON><PERSON> ho<PERSON>t động", "function.user.modal-item.status-false.label": "Ngừng hoạt động", "function.user.modal-item.user-profile": "Thông tin cá nhân", "function.user.modal-item.account-profile": "Thông tin tài k<PERSON>n", "function.user.model-item-header-info": "<PERSON> tiết người dùng", "function.user.modal-item.error.message.form-invalid": "<PERSON><PERSON>m tra lại thông tin các trường đã nhập!", "function.user.send-mail-forgot-pass-modal.header": "<PERSON><PERSON><PERSON> nhận gửi mail thông báo quên mật khẩu", "function.user.send-mail-forgot-pass-modal.content": "Bạn có chắc chắn muốn gửi mail thông báo cập nhật mật khẩu mới", "function.user.send-mail-forgot-pass-completed": "Gửi email thành công", "function.user.send-mail-forgot-pass-fail": "Gửi email thất bại", "function.add-role.modal-item.header-edit": "<PERSON><PERSON><PERSON> nhật phân quyền người dùng", "function.add-role.modal-item.issystemadmin": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON> hệ thống", "function.add-role.modal-item.isorgadmin": "<PERSON><PERSON><PERSON>n trị đơn vị", "function.add-role.modal-item.isuser": "<PERSON><PERSON><PERSON><PERSON> dùng", "menu.system": "<PERSON><PERSON> th<PERSON>", "menu.permission": "<PERSON><PERSON> quyền", "menu.user": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng", "menu.log": "<PERSON><PERSON><PERSON><PERSON> ký", "menu.system-log": "<PERSON><PERSON><PERSON><PERSON> ký hệ thống", "menu.email-log": "Nhật ký gửi mail", "menu.workflow": "<PERSON><PERSON> tr<PERSON>nh", "menu.forgot-password-log": "<PERSON><PERSON><PERSON><PERSON> ký quên mật khẩu", "menu.role": "<PERSON>hóm ng<PERSON>ời dùng", "menu.email-template": "Mẫu email", "password-change.required": "<PERSON><PERSON><PERSON> kh<PERSON>u không đư<PERSON><PERSON> để trống", "password-change.password-strength": "<PERSON><PERSON><PERSON> khẩu tối thiểu 8 ký tự, có chữ hoa, chữ thườ<PERSON>, số và ký tự đặc biệt", "password-change-confirm.required": "<PERSON><PERSON><PERSON> khẩu xác nhận không đư<PERSON><PERSON> để trống", "password-change-confirm.password-mismatch": "<PERSON><PERSON><PERSON> khẩu xác nhận phải giống với mật khẩu", "menu.catalog": "<PERSON><PERSON>", "menu.training-system": "<PERSON><PERSON> đào tạo", "function.training-system.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.training-system.table.code": "<PERSON><PERSON>", "function.training-system.table.name": "<PERSON><PERSON><PERSON>", "function.training-system.table.regulation": "<PERSON><PERSON>", "function.training-system.table.education-level-name": "<PERSON><PERSON><PERSON> đạo tạo", "function.training-system.table.form-of-training": "<PERSON><PERSON><PERSON> thức đào tạo", "function.training-system.table.degree-number": "Stt trình độ", "function.training-system.page.title": "<PERSON><PERSON> s<PERSON>ch hệ đào tạo", "function.training-system.modal.form.code": "<PERSON><PERSON> hệ đào tạo", "function.training-system.modal.form.code.required": "<PERSON><PERSON> hệ không đư<PERSON><PERSON> để trống", "function.training-system.modal.form.code.place-holder": "<PERSON><PERSON> hệ đào tạo", "function.training-system.modal.form.name": "<PERSON><PERSON><PERSON> hệ đào tạo", "function.training-system.modal.form.name.required": "<PERSON>ê<PERSON> hệ đào tạo không đư<PERSON><PERSON> để trống", "function.training-system.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> hệ đào tạo", "function.training-system.modal.form.name-En": "<PERSON>ê<PERSON> hệ đào tạo bằng tiếng anh", "function.training-system.modal.form.name-En.required": "Tên hệ đào tạo bằng tiếng anh không được để trống", "function.training-system.modal.form.name-En.place-holder": "<PERSON>ê<PERSON> hệ đào tạo bằng tiếng anh", "function.training-system.modal.form.regulation": "<PERSON><PERSON>", "function.training-system.modal.form.regulation.place-holder": "<PERSON><PERSON>", "function.training-system.modal.form.education-level-name": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đào tạo", "function.training-system.modal.form.education-level-name.required": "<PERSON><PERSON><PERSON> bậc đào tạo không đư<PERSON><PERSON> để trống", "function.training-system.modal.form.education-level-name.place-holder": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đào tạo", "function.training-system.modal.form.education-level-name-En": "<PERSON><PERSON><PERSON> b<PERSON>c đào tạo bằng tiếng anh", "function.training-system.modal.form.education-level-name-En.required": "<PERSON><PERSON><PERSON> bậc đào tạo bằng tiếng anh không được để trống", "function.training-system.modal.form.education-level-name-En.place-holder": "<PERSON><PERSON><PERSON> b<PERSON>c đào tạo bằng tiếng anh", "function.training-system.modal.form.form-of-training": "<PERSON><PERSON><PERSON> thức đào tạo", "function.training-system.modal.form.form-of-training.required": "<PERSON><PERSON><PERSON> thức đào tạo không đư<PERSON><PERSON> để trống", "function.training-system.modal.form.form-of-training.place-holder": "<PERSON><PERSON><PERSON> thức đào tạo", "function.training-system.modal.form.form-of-training-En": "<PERSON><PERSON><PERSON> thức đào tạo bằng tiếng anh", "function.training-system.modal.form.form-of-training-En.required": "<PERSON><PERSON><PERSON> thức đào tạo bằng tiếng anh không được để trống", "function.training-system.modal.form.form-of-training-En.place-holder": "<PERSON><PERSON><PERSON> thức đào tạo bằng tiếng anh", "function.training-system.modal.form.degree-number": "Stt trình độ", "function.training-system.modal.form.degree-number.required": "Stt trình độ không được để trống", "function.training-system.modal.form.degree-number.place-holder": "Stt trình độ", "function.training-system.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> mới hệ đào tạo", "function.training-system.modal.title-info": "<PERSON><PERSON> chi tết hệ đào tạo", "function.training-system.modal.title-edit": "<PERSON><PERSON><PERSON> hệ đào tạo", "function.training-system.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.training-system.confirm-delete.content": "Bạn có chắc chắn muốn xóa hệ đào tạo này", "menu.religion": "Tôn gi<PERSON>o", "function.religion.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.religion.table.code": "Mã tôn giáo", "function.religion.table.name": "Tôn gi<PERSON>o", "function.religion.table.name-en": "<PERSON><PERSON><PERSON>(English)", "function.religion.modal.form.code": "Mã tôn giáo ", "function.religion.modal.form.code.required": "<PERSON>ã tôn gi<PERSON><PERSON> không được để trống", "function.religion.modal.form.code.place-holder": "Mã tôn giáo ", "function.religion.modal.form.name": "<PERSON>ên tôn gi<PERSON>o ", "function.religion.modal.form.name.required": "Tên tôn gi<PERSON><PERSON> không được để trống", "function.religion.modal.form.name.place-holder": "<PERSON>ên tôn gi<PERSON>o ", "function.religion.modal.form.name-En": "<PERSON><PERSON><PERSON> tôn <PERSON>(English)", "function.religion.modal.form.name-En.required": "Tên tôn gi<PERSON>o  bằng tiếng anh không được để trống", "function.religion.modal.form.name-En.place-holder": "Tên tôn giáo  bằng tiếng anh", "function.religion.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.religion.confirm-delete.content": "Bạn có chắc chắn muốn xóa tôn gi<PERSON>o này", "function.religion.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> tôn gi<PERSON>o", "function.religion.modal.title-info": "<PERSON>em chi tiết tôn gi<PERSON>o", "function.religion.modal.title-edit": "<PERSON><PERSON><PERSON> tôn gi<PERSON>o", "function.religion.page.title": "<PERSON><PERSON> s<PERSON>ch tôn gi<PERSON>o", "menu.nationality": "<PERSON><PERSON><PERSON><PERSON>", "function.nationality.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.nationality.table.code": "<PERSON><PERSON> quốc tịch", "function.nationality.table.name": "<PERSON><PERSON><PERSON><PERSON>", "function.nationality.table.name-en": "<PERSON><PERSON><PERSON><PERSON>(English)", "function.nationality.modal.form.code": "<PERSON><PERSON> quốc tịch ", "function.nationality.modal.form.code.required": "<PERSON><PERSON> quốc tịch không đ<PERSON><PERSON>c để trống", "function.nationality.modal.form.code.place-holder": "<PERSON><PERSON> quốc tịch ", "function.nationality.modal.form.name": "<PERSON><PERSON><PERSON> qu<PERSON><PERSON> tịch ", "function.nationality.modal.form.name.required": "<PERSON><PERSON><PERSON> quố<PERSON> tịch không đư<PERSON>c để trống", "function.nationality.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> qu<PERSON><PERSON> tịch ", "function.nationality.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.nationality.confirm-delete.content": "Bạn có chắc chắn muốn xóa quốc tịch này", "function.nationality.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> quốc tịch", "function.nationality.modal.title-info": "<PERSON>em chi tiết quốc tịch", "function.nationality.modal.title-edit": "<PERSON><PERSON><PERSON> quốc tịch", "function.nationality.page.title": "<PERSON><PERSON> s<PERSON>ch quốc tịch", "menu.ethnic": "<PERSON><PERSON> t<PERSON>c", "function.ethnic.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.ethnic.table.code": "<PERSON>ã dân tộc", "function.ethnic.table.name": "<PERSON><PERSON> t<PERSON>c", "function.ethnic.table.name-en": "<PERSON><PERSON>(English)", "function.ethnic.modal.form.code": "<PERSON>ã dân tộc ", "function.ethnic.modal.form.code.required": "<PERSON>ã dân tộc không được để trống", "function.ethnic.modal.form.code.place-holder": "<PERSON>ã dân tộc ", "function.ethnic.modal.form.name": "<PERSON><PERSON><PERSON> dân tộc ", "function.ethnic.modal.form.name.required": "<PERSON>ên dân tộc không đư<PERSON>c để trống", "function.ethnic.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> dân tộc ", "function.ethnic.modal.form.name-En": "<PERSON><PERSON><PERSON>(English)", "function.ethnic.modal.form.name-En.required": "Tên dân tộc  bằng tiếng anh không được để trống", "function.ethnic.modal.form.name-En.place-holder": "<PERSON>ê<PERSON> dân tộc  bằng tiếng anh", "function.ethnic.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.ethnic.confirm-delete.content": "Bạn có chắc chắn muốn xóa dân tộc này", "function.ethnic.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> dân tộc", "function.ethnic.modal.title-info": "<PERSON>em chi tiết dân tộc", "function.ethnic.modal.title-edit": "<PERSON><PERSON><PERSON> dân tộc", "function.ethnic.page.title": "<PERSON><PERSON> s<PERSON>ch dân tộc", "menu.gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "function.gender.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.gender.table.name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "function.gender.modal.form.id": "Id gi<PERSON>i t<PERSON>h ", "function.gender.modal.form.id.required": "Id gi<PERSON><PERSON> t<PERSON>h không đư<PERSON>c để trống", "function.gender.modal.form.id.place-holder": "Id gi<PERSON>i t<PERSON>h ", "function.gender.modal.form.name": "<PERSON><PERSON>n g<PERSON><PERSON> t<PERSON>h ", "function.gender.modal.form.name.required": "Tên gi<PERSON>i t<PERSON>h không được để trống", "function.gender.modal.form.name.place-holder": "<PERSON><PERSON>n g<PERSON><PERSON> t<PERSON>h ", "function.gender.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.gender.confirm-delete.content": "Bạn có chắc chắn muốn xóa giới t<PERSON>h này", "function.gender.modal.title-add": "<PERSON>h<PERSON><PERSON> gi<PERSON>i t<PERSON>h", "function.gender.modal.title-info": "<PERSON>em chi tiết gi<PERSON>i t<PERSON>h", "function.gender.modal.title-edit": "<PERSON><PERSON>a gi<PERSON>i t<PERSON>h", "function.gender.page.title": "<PERSON><PERSON> s<PERSON>ch gi<PERSON>i t<PERSON>h", "menu.degree": "H<PERSON><PERSON> vị", "function.degree.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.degree.table.code": "Mã học vị", "function.degree.table.name": "H<PERSON><PERSON> vị", "function.degree.table.course-compensation-coefficient": "<PERSON><PERSON> số bù kh<PERSON>a học", "function.degree.modal.form.code": "Mã học vị ", "function.degree.modal.form.code.required": "<PERSON><PERSON> học vị không đư<PERSON>c để trống", "function.degree.modal.form.code.place-holder": "Mã học vị ", "function.degree.modal.form.name": "<PERSON><PERSON><PERSON> vị ", "function.degree.modal.form.name.required": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> vị không đ<PERSON><PERSON><PERSON> để trống", "function.degree.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> vị ", "function.degree.modal.form.course-compensation-coefficient": "<PERSON><PERSON> số bù kh<PERSON>a học", "function.degree.modal.form.course-compensation-coefficient.required": "<PERSON><PERSON> số bù khóa học không đư<PERSON><PERSON> để trống", "function.degree.modal.form.course-compensation-coefficient.place-holder": "<PERSON><PERSON> số bù kh<PERSON>a học", "function.degree.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.degree.confirm-delete.content": "Bạn có chắc chắn muốn xóa học vị này", "function.degree.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> vị", "function.degree.modal.title-info": "<PERSON>em chi ti<PERSON><PERSON> h<PERSON> vị", "function.degree.modal.title-edit": "<PERSON><PERSON><PERSON> vị", "function.degree.page.title": "<PERSON><PERSON> s<PERSON> h<PERSON> vị", "menu.academic-rank": "<PERSON><PERSON><PERSON>", "function.academic-rank.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.academic-rank.table.code": "<PERSON><PERSON> học hàm", "function.academic-rank.table.name": "<PERSON><PERSON><PERSON>", "function.academic-rank.table.name-en": "<PERSON><PERSON><PERSON>(English)", "function.academic-rank.modal.form.code": "<PERSON><PERSON> học hàm ", "function.academic-rank.modal.form.code.required": "<PERSON><PERSON> học hàm không đư<PERSON><PERSON> để trống", "function.academic-rank.modal.form.code.place-holder": "<PERSON><PERSON> học hàm ", "function.academic-rank.modal.form.name": "<PERSON><PERSON><PERSON> h<PERSON>m ", "function.academic-rank.modal.form.name.required": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> hàm không đư<PERSON>c để trống", "function.academic-rank.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> h<PERSON>m ", "function.academic-rank.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.academic-rank.confirm-delete.content": "Bạn có chắc chắn muốn xóa học hàm này", "function.academic-rank.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> h<PERSON>m", "function.academic-rank.modal.title-info": "<PERSON>em chi ti<PERSON><PERSON> h<PERSON> hàm", "function.academic-rank.modal.title-edit": "<PERSON><PERSON><PERSON> h<PERSON> h<PERSON>m", "function.academic-rank.page.title": "<PERSON><PERSON> s<PERSON> h<PERSON> h<PERSON>m", "menu.department": "<PERSON><PERSON><PERSON>", "function.department.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.department.table.code": "Mã khoa", "function.department.table.name": "<PERSON><PERSON><PERSON>", "function.department.table.name-en": "<PERSON><PERSON><PERSON>(English)", "function.department.modal.form.code": "Mã khoa ", "function.department.modal.form.code.required": "<PERSON><PERSON> khoa không đư<PERSON>c để trống", "function.department.modal.form.code.place-holder": "Mã khoa ", "function.department.modal.form.name": "<PERSON><PERSON><PERSON> k<PERSON>a ", "function.department.modal.form.name.required": "<PERSON><PERSON><PERSON> khoa không đư<PERSON>c để trống", "function.department.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> k<PERSON>a ", "function.department.modal.form.name-En": "<PERSON><PERSON><PERSON>(English)", "function.department.modal.form.name-En.required": "<PERSON><PERSON><PERSON> khoa bằng tiếng anh không được để trống", "function.department.modal.form.name-En.place-holder": "<PERSON><PERSON><PERSON> khoa bằng tiếng anh", "function.department.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.department.confirm-delete.content": "Bạn có chắc chắn muốn xóa khoa này", "function.department.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>a", "function.department.modal.title-info": "<PERSON>em chi ti<PERSON>t khoa", "function.department.modal.title-edit": "<PERSON><PERSON><PERSON> khoa", "function.department.page.title": "<PERSON><PERSON> s<PERSON> khoa", "menu.industry": "<PERSON><PERSON><PERSON>", "function.industry.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.industry.table.code": "<PERSON><PERSON> ng<PERSON>", "function.industry.table.name": "<PERSON><PERSON><PERSON>", "function.industry.table.name-en": "<PERSON><PERSON><PERSON>(English)", "function.industry.table.pedagogy": "<PERSON><PERSON> ph<PERSON>", "function.industry.modal.form.code": "<PERSON><PERSON> ng<PERSON> ", "function.industry.modal.form.code.required": "<PERSON><PERSON> ngành không đư<PERSON>c để trống", "function.industry.modal.form.code.place-holder": "<PERSON><PERSON> ng<PERSON> ", "function.industry.modal.form.name": "<PERSON><PERSON><PERSON> ", "function.industry.modal.form.name.required": "<PERSON><PERSON><PERSON> ngành không được để trống", "function.industry.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> ", "function.industry.modal.form.name-En": "<PERSON><PERSON><PERSON>(English)", "function.industry.modal.form.name-En.required": "<PERSON><PERSON><PERSON> ngành  bằng tiếng anh không được để trống", "function.industry.modal.form.name-En.place-holder": "<PERSON><PERSON><PERSON> ngành  bằng tiếng anh", "function.industry.modal.form.pedagogy": "<PERSON><PERSON> ph<PERSON>", "function.industry.modal.form.pedagogy.required": "<PERSON><PERSON> phạm không đ<PERSON><PERSON><PERSON> để trống", "function.industry.modal.form.pedagogy.place-holder": "<PERSON><PERSON> ph<PERSON>", "function.industry.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.industry.confirm-delete.content": "Bạn có chắc chắn muốn xóa ngành này", "function.industry.modal.title-add": "<PERSON><PERSON><PERSON><PERSON>", "function.industry.modal.title-info": "<PERSON>em chi tiết ng<PERSON>nh", "function.industry.modal.title-edit": "<PERSON><PERSON><PERSON>", "function.industry.page.title": "<PERSON><PERSON> s<PERSON> ng<PERSON>nh", "menu.majors": "<PERSON><PERSON><PERSON><PERSON>", "function.majors.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.majors.table.code": "<PERSON><PERSON> chuyên ng<PERSON>nh", "function.majors.table.name": "<PERSON><PERSON><PERSON><PERSON>", "function.majors.table.name-en": "<PERSON><PERSON><PERSON><PERSON>(English)", "function.majors.table.name-industry": "<PERSON><PERSON><PERSON>", "function.majors.modal.form.code": "<PERSON><PERSON> chuyên ng<PERSON>nh ", "function.majors.modal.form.code.required": "<PERSON><PERSON> chuyên ngành không được để trống", "function.majors.modal.form.code.place-holder": "<PERSON><PERSON> chuyên ng<PERSON>nh ", "function.majors.modal.form.name": "<PERSON><PERSON><PERSON> chuyên ng<PERSON>nh ", "function.majors.modal.form.name.required": "<PERSON>ê<PERSON> chuyên ngành không được để trống", "function.majors.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> chuyên ng<PERSON>nh ", "function.majors.modal.form.name-En": "<PERSON><PERSON><PERSON> chuyê<PERSON>(English)", "function.majors.modal.form.name-En.required": "<PERSON><PERSON><PERSON> chuyên ngành  bằng tiếng anh không được để trống", "function.majors.modal.form.name-En.place-holder": "<PERSON><PERSON><PERSON> chuyên ngành  bằng tiếng anh", "function.majors.modal.form.name-industry": "<PERSON><PERSON><PERSON>", "function.majors.modal.form.name-industry.required": "<PERSON><PERSON><PERSON> ng<PERSON>nh anh không đư<PERSON><PERSON> để trống", "function.majors.modal.form.name-industry.place-holder": "<PERSON><PERSON><PERSON>", "function.majors.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.majors.confirm-delete.content": "Bạn có chắc chắn muốn xóa chuyên ngành này", "function.majors.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> chuyên ng<PERSON>nh", "function.majors.modal.title-info": "<PERSON>em chi tiết chuyên ng<PERSON>nh", "function.majors.modal.title-edit": "<PERSON><PERSON><PERSON> chuy<PERSON>n ng<PERSON>nh", "function.majors.page.title": "<PERSON><PERSON> s<PERSON>ch chuyên ng<PERSON>nh", "menu.province": "Tỉnh", "function.province.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.province.table.code": " Mã tỉnh", "function.province.table.name": "Tỉnh", "function.province.table.name-en": "Tỉnh(English)", "function.province.modal.form.code": "Mã tỉnh ", "function.province.modal.form.code.required": "<PERSON><PERSON> tỉnh không được để trống", "function.province.modal.form.code.place-holder": "Mã tỉnh ", "function.province.modal.form.name": "<PERSON><PERSON>n tỉnh ", "function.province.modal.form.name.required": "Tên tỉnh không được để trống", "function.province.modal.form.name.place-holder": "<PERSON><PERSON>n tỉnh ", "function.province.modal.form.name-En": "<PERSON><PERSON><PERSON> tỉnh(English)", "function.province.modal.form.name-En.required": "Tên tỉnh  bằng tiếng anh không được để trống", "function.province.modal.form.name-En.place-holder": "Tên tỉnh  bằng tiếng anh", "function.province.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.province.confirm-delete.content": "Bạn có chắc chắn muốn xóa tỉnh này", "function.province.modal.title-add": "Thê<PERSON> tỉnh", "function.province.modal.title-info": "<PERSON>em chi tiết tỉnh", "function.province.modal.title-edit": "<PERSON><PERSON><PERSON> tỉnh", "function.province.page.title": "<PERSON><PERSON> s<PERSON>ch tỉnh", "menu.district": "<PERSON><PERSON><PERSON><PERSON>", "function.district.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.district.table.name-province": "<PERSON><PERSON>n tỉnh", "function.district.table.name": "<PERSON><PERSON><PERSON><PERSON>", "function.district.table.code": "<PERSON><PERSON> h<PERSON>", "function.district.table.name-en": "<PERSON><PERSON><PERSON><PERSON>(English)", "function.district.table.id-old": "<PERSON><PERSON> c<PERSON>", "function.district.table.id-old-1": "<PERSON><PERSON> <PERSON> c<PERSON> 1", "function.district.table.name-old": "<PERSON><PERSON><PERSON> huy<PERSON> cũ", "function.district.modal.form.code": "<PERSON><PERSON> h<PERSON> ", "function.district.modal.form.code.required": "<PERSON><PERSON> huyện không được để trống", "function.district.modal.form.code.place-holder": "<PERSON><PERSON> h<PERSON> ", "function.district.modal.form.name-province": "<PERSON><PERSON>n tỉnh ", "function.district.modal.form.name-province.required": "Tên tỉnh không được để trống", "function.district.modal.form.name-province.place-holder": "<PERSON><PERSON>n tỉnh ", "function.district.modal.form.id-old": "<PERSON><PERSON> c<PERSON> ", "function.district.modal.form.id-old.required": "Id h<PERSON><PERSON><PERSON> cũ không đư<PERSON><PERSON> để trống", "function.district.modal.form.id-old.place-holder": "<PERSON><PERSON> c<PERSON> ", "function.district.modal.form.id-old-1": "<PERSON><PERSON> <PERSON> c<PERSON> 1 ", "function.district.modal.form.id-old-1.required": "Id <PERSON> cũ 1 không đư<PERSON>c để trống", "function.district.modal.form.id-old-1.place-holder": "<PERSON><PERSON> <PERSON> c<PERSON> 1", "function.district.modal.form.name": "<PERSON><PERSON><PERSON> ", "function.district.modal.form.name.required": "<PERSON><PERSON><PERSON> huy<PERSON>n không đư<PERSON><PERSON> để trống", "function.district.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> ", "function.district.modal.form.name-En": "<PERSON><PERSON><PERSON>(English)", "function.district.modal.form.name-En.required": "Tê<PERSON> huyện  bằng tiếng anh không được để trống", "function.district.modal.form.name-En.place-holder": "<PERSON><PERSON><PERSON> huyện  bằng tiếng anh", "function.district.modal.form.name-old": "<PERSON><PERSON><PERSON> huy<PERSON> cũ", "function.district.modal.form.name-old.required": "<PERSON><PERSON><PERSON> huyện cũ không được để trống", "function.district.modal.form.name-old.place-holder": "<PERSON><PERSON><PERSON> huy<PERSON> cũ", "function.district.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.district.confirm-delete.content": "Bạn có chắc chắn muốn xóa huyện này", "function.district.modal.title-add": "<PERSON><PERSON><PERSON><PERSON>", "function.district.modal.title-info": "<PERSON><PERSON> chi ti<PERSON><PERSON> h<PERSON>", "function.district.modal.title-edit": "<PERSON><PERSON><PERSON>", "function.district.page.title": "<PERSON><PERSON> h<PERSON>", "menu.area": "<PERSON><PERSON> v<PERSON>", "function.area.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.area.table.code": "Mã khu vực", "function.area.table.name": "<PERSON><PERSON> v<PERSON>", "function.area.table.diem-cong-kv": "<PERSON><PERSON><PERSON><PERSON> cộng khu vực", "function.area.modal.form.code": "Mã khu vực ", "function.area.modal.form.code.required": "<PERSON>ã khu vực không được để trống", "function.area.modal.form.code.place-holder": "Mã khu vực ", "function.area.modal.form.name": "<PERSON><PERSON><PERSON> khu vực ", "function.area.modal.form.name.required": "<PERSON><PERSON><PERSON> khu vự<PERSON> không được để trống", "function.area.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> khu vực ", "function.area.modal.form.diem-cong-kv": "<PERSON><PERSON><PERSON><PERSON> cộng khu vực", "function.area.modal.form.diem-cong-kv.required": "<PERSON><PERSON><PERSON><PERSON> cộng khu vực không được để trống", "function.area.modal.form.diem-cong-kv.place-holder": "<PERSON><PERSON><PERSON><PERSON> cộng khu vực", "function.area.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.area.confirm-delete.content": "Bạn có chắc chắn muốn xóa khu vực này", "function.area.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> khu vực", "function.area.modal.title-info": "<PERSON>em chi tiết khu vực", "function.area.modal.title-edit": "<PERSON>ử<PERSON> khu vực", "function.area.page.title": "<PERSON><PERSON> s<PERSON>ch khu vực", "menu.object-group": "<PERSON><PERSON><PERSON><PERSON> đố<PERSON> t<PERSON>", "function.object-group.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.object-group.table.code": "<PERSON><PERSON> nhóm đối tư<PERSON>", "function.object-group.table.name": "<PERSON><PERSON><PERSON><PERSON> đố<PERSON> t<PERSON>", "function.object-group.table.name-en": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>(English)", "function.object-group.modal.form.code": "<PERSON><PERSON> nhóm đối tư<PERSON> ", "function.object-group.modal.form.code.required": "<PERSON>ã nhóm đối tượng không được để trống", "function.object-group.modal.form.code.place-holder": "<PERSON><PERSON> nhóm đối tư<PERSON> ", "function.object-group.modal.form.name": "<PERSON><PERSON><PERSON> nh<PERSON>m đối tư<PERSON> ", "function.object-group.modal.form.name.required": "<PERSON>ê<PERSON> nh<PERSON>m đối tượng không đượ<PERSON> để trống", "function.object-group.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> nh<PERSON>m đối tư<PERSON> ", "function.object-group.modal.form.name-En": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> đ<PERSON>(English)", "function.object-group.modal.form.name-En.required": "Tê<PERSON> nhóm đối tượng  bằng tiếng anh không được để trống", "function.object-group.modal.form.name-En.place-holder": "<PERSON><PERSON><PERSON> nh<PERSON>m đối tượng  bằng tiếng anh", "function.object-group.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.object-group.confirm-delete.content": "Bạn có chắc chắn muốn xóa nhóm đối tượng này", "function.object-group.modal.title-add": "<PERSON>h<PERSON><PERSON> nhóm đối tư<PERSON>", "function.object-group.modal.title-info": "<PERSON>em chi tiết nhóm đối tượng", "function.object-group.modal.title-edit": "<PERSON><PERSON><PERSON> nhóm đối tư<PERSON>", "function.object-group.page.title": "<PERSON><PERSON> s<PERSON>ch nhóm đối tư<PERSON>", "menu.object": "<PERSON><PERSON><PERSON>", "function.object.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.object.table.code": "<PERSON><PERSON> đối tư<PERSON>", "function.object.table.name": "<PERSON><PERSON><PERSON>", "function.object.table.phan-tram-mien-giam": "<PERSON><PERSON><PERSON> tr<PERSON>m miễn <PERSON>", "function.object.modal.form.code": "<PERSON><PERSON> đối tư<PERSON> ", "function.object.modal.form.code.required": "<PERSON>ã đối tượng không được để trống", "function.object.modal.form.code.place-holder": "<PERSON><PERSON> đối tư<PERSON> ", "function.object.modal.form.name": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> t<PERSON> ", "function.object.modal.form.name.required": "Tên đối tượng không được để trống", "function.object.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> t<PERSON> ", "function.object.modal.form.phan-tram-mien-giam": "<PERSON><PERSON><PERSON> tr<PERSON>m miễn <PERSON>", "function.object.modal.form.phan-tram-mien-giam.required": "<PERSON><PERSON><PERSON> tr<PERSON>m miễn gi<PERSON>m không đ<PERSON><PERSON><PERSON> để trống", "function.object.modal.form.phan-tram-mien-giam.place-holder": "<PERSON><PERSON><PERSON> tr<PERSON>m miễn <PERSON>", "function.object.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.object.confirm-delete.content": "Bạn có chắc chắn muốn xóa đối tượng này", "function.object.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> t<PERSON>", "function.object.modal.title-info": "<PERSON>em chi tiết đối tư<PERSON>", "function.object.modal.title-edit": "<PERSON><PERSON><PERSON> đ<PERSON>i t<PERSON>", "function.object.page.title": "<PERSON><PERSON> s<PERSON>ch đối t<PERSON>", "menu.scholarship-object": "<PERSON><PERSON><PERSON> t<PERSON> h<PERSON> bổng", "function.scholarship-object.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.scholarship-object.table.code": "<PERSON><PERSON> đối tư<PERSON><PERSON> h<PERSON> bổng", "function.scholarship-object.table.name": "<PERSON><PERSON><PERSON> t<PERSON> h<PERSON> bổng", "function.scholarship-object.table.so-tien-tro-cap": "<PERSON><PERSON> tiền trợ cấp", "function.scholarship-object.table.phan-tram-tro-cap": "<PERSON><PERSON><PERSON> tr<PERSON>m trợ cấp", "function.scholarship-object.modal.form.code": "<PERSON><PERSON> đối tư<PERSON><PERSON> h<PERSON> bổng ", "function.scholarship-object.modal.form.code.required": "<PERSON>ã đối tượ<PERSON> học bổng không đư<PERSON><PERSON> để trống", "function.scholarship-object.modal.form.code.place-holder": "<PERSON><PERSON> đối tư<PERSON><PERSON> h<PERSON> bổng ", "function.scholarship-object.modal.form.name": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> t<PERSON> h<PERSON> bổng ", "function.scholarship-object.modal.form.name.required": "Tên đố<PERSON> tượ<PERSON> học bổng không đư<PERSON><PERSON> để trống", "function.scholarship-object.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> t<PERSON> h<PERSON> bổng ", "function.scholarship-object.modal.form.so-tien-tro-cap": "<PERSON><PERSON> tiền trợ cấp", "function.scholarship-object.modal.form.so-tien-tro-cap.required": "Số tiền trợ cấp không đư<PERSON><PERSON> để trống", "function.scholarship-object.modal.form.so-tien-tro-cap.place-holder": "<PERSON><PERSON> tiền trợ cấp", "function.scholarship-object.modal.form.phan-tram-tro-cap": "<PERSON><PERSON><PERSON> tr<PERSON>m trợ cấp", "function.scholarship-object.modal.form.phan-tram-tro-cap.required": "<PERSON>ầ<PERSON> trăm trợ cấp không đư<PERSON><PERSON> để trống", "function.scholarship-object.modal.form.phan-tram-tro-cap.place-holder": "<PERSON><PERSON><PERSON> tr<PERSON>m trợ cấp", "function.scholarship-object.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.scholarship-object.confirm-delete.content": "Bạn có chắc chắn muốn xóa đối tượ<PERSON> học bổng này", "function.scholarship-object.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> tư<PERSON> h<PERSON> bổng", "function.scholarship-object.modal.title-info": "<PERSON>em chi tiết đối tư<PERSON><PERSON> h<PERSON> bổng", "function.scholarship-object.modal.title-edit": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> tư<PERSON><PERSON> h<PERSON> bổng", "function.scholarship-object.page.title": "<PERSON><PERSON> s<PERSON>ch đối tư<PERSON> h<PERSON> bổng", "menu.lever": "<PERSON><PERSON><PERSON> khen thưởng kỷ luật", "function.lever.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.lever.table.code": "<PERSON><PERSON> cấp", "function.lever.table.name": "<PERSON><PERSON><PERSON> c<PERSON>", "function.lever.modal.form.code": "<PERSON><PERSON> cấp ", "function.lever.modal.form.code.required": "<PERSON><PERSON> cấp không đư<PERSON><PERSON> để trống", "function.lever.modal.form.code.place-holder": "<PERSON><PERSON> cấp ", "function.lever.modal.form.name": "<PERSON><PERSON><PERSON> c<PERSON> ", "function.lever.modal.form.name.required": "<PERSON><PERSON><PERSON> cấ<PERSON> không đư<PERSON><PERSON> để trống", "function.lever.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> c<PERSON> ", "function.lever.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.lever.confirm-delete.content": "Bạn có chắc chắn muốn xóa cấp này", "function.lever.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>p", "function.lever.modal.title-info": "<PERSON>em chi ti<PERSON><PERSON> cấp", "function.lever.modal.title-edit": "<PERSON><PERSON><PERSON> c<PERSON>p", "function.lever.page.title": "<PERSON><PERSON> s<PERSON> cấp", "menu.reward-type": "<PERSON><PERSON><PERSON> khen thưởng", "function.reward-type.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.reward-type.table.ten-cap": "<PERSON><PERSON><PERSON> c<PERSON>", "function.reward-type.table.name": "<PERSON><PERSON><PERSON> khen thưởng", "function.reward-type.table.diem-thuong": "<PERSON><PERSON><PERSON><PERSON> thưởng", "function.reward-type.modal.form.ten-cap": "<PERSON><PERSON><PERSON> c<PERSON> ", "function.reward-type.modal.form.ten-cap.required": "<PERSON><PERSON><PERSON> cấ<PERSON> không đư<PERSON><PERSON> để trống", "function.reward-type.modal.form.ten-cap.place-holder": "<PERSON><PERSON><PERSON> c<PERSON> ", "function.reward-type.modal.form.name": "<PERSON><PERSON><PERSON> lo<PERSON> khen thưởng ", "function.reward-type.modal.form.name.required": "<PERSON>ê<PERSON> lo<PERSON>i khen thưởng không được để trống", "function.reward-type.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> lo<PERSON> khen thưởng ", "function.reward-type.modal.form.diem-thuong": "<PERSON><PERSON><PERSON><PERSON> thưởng", "function.reward-type.modal.form.diem-thuong.required": "<PERSON><PERSON><PERSON><PERSON> thưởng không được để trống", "function.reward-type.modal.form.diem-thuong.place-holder": "<PERSON><PERSON><PERSON><PERSON> thưởng", "function.reward-type.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.reward-type.confirm-delete.content": "Bạn có chắc chắn muốn xóa loại khen thưởng này", "function.reward-type.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON> khen thưởng", "function.reward-type.modal.title-info": "<PERSON>em chi tiết lo<PERSON>i khen thưởng", "function.reward-type.modal.title-edit": "<PERSON><PERSON><PERSON> lo<PERSON>i khen thưởng", "function.reward-type.page.title": "<PERSON><PERSON> s<PERSON>ch lo<PERSON>i khen thưởng", "menu.behavior": "Hành vi", "function.behavior.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.behavior.table.code": "Mã hành vi", "function.behavior.table.name": "Hành vi", "function.behavior.modal.form.code": "Mã hành vi ", "function.behavior.modal.form.code.required": "<PERSON><PERSON> hành vi không đư<PERSON>c để trống", "function.behavior.modal.form.code.place-holder": "Mã hành vi ", "function.behavior.modal.form.name": "<PERSON><PERSON><PERSON> h<PERSON>nh vi ", "function.behavior.modal.form.name.required": "<PERSON><PERSON><PERSON> hành vi không đư<PERSON><PERSON> để trống", "function.behavior.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> h<PERSON>nh vi ", "function.behavior.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.behavior.confirm-delete.content": "Bạn có chắc chắn muốn xóa loại khen thưởng này", "function.behavior.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON> khen thưởng", "function.behavior.modal.title-info": "<PERSON>em chi tiết lo<PERSON>i khen thưởng", "function.behavior.modal.title-edit": "<PERSON><PERSON><PERSON> lo<PERSON>i khen thưởng", "function.behavior.page.title": "<PERSON><PERSON> s<PERSON>ch lo<PERSON>i khen thưởng", "menu.handle": "<PERSON><PERSON> lý", "function.handle.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.handle.table.ten-cap": "<PERSON><PERSON><PERSON>", "function.handle.table.name": "<PERSON><PERSON> lý", "function.handle.table.so-thang": "<PERSON><PERSON> tháng", "function.handle.table.diem-phat": "<PERSON><PERSON><PERSON><PERSON>", "function.handle.table.muc-xu-ly": "<PERSON><PERSON><PERSON> lý", "function.handle.modal.form.ten-cap": "<PERSON><PERSON><PERSON> ", "function.handle.modal.form.ten-cap.required": "<PERSON><PERSON><PERSON> câ<PERSON> không đư<PERSON><PERSON> để trống", "function.handle.modal.form.ten-cap.place-holder": "<PERSON><PERSON><PERSON> ", "function.handle.modal.form.name": "<PERSON><PERSON> lý ", "function.handle.modal.form.name.required": "<PERSON><PERSON> lý không đ<PERSON><PERSON><PERSON> để trống", "function.handle.modal.form.name.place-holder": "<PERSON><PERSON> lý ", "function.handle.modal.form.so-thang": "<PERSON><PERSON> tháng ", "function.handle.modal.form.so-thang.required": "<PERSON><PERSON> tháng không đư<PERSON><PERSON> để trống", "function.handle.modal.form.so-thang.place-holder": "<PERSON><PERSON> tháng", "function.handle.modal.form.diem-phat": "<PERSON><PERSON><PERSON><PERSON> phat ", "function.handle.modal.form.diem-phat.required": "<PERSON><PERSON><PERSON><PERSON> phat không đ<PERSON><PERSON><PERSON> để trống", "function.handle.modal.form.diem-phat.place-holder": "<PERSON><PERSON><PERSON><PERSON> phat ", "function.handle.modal.form.muc-xu-ly": "<PERSON><PERSON><PERSON> lý ", "function.handle.modal.form.muc-xu-ly.required": "<PERSON><PERSON><PERSON> xử lý không đ<PERSON><PERSON><PERSON> để trống", "function.handle.modal.form.muc-xu-ly.place-holder": "<PERSON><PERSON><PERSON> lý ", "function.handle.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.handle.confirm-delete.content": "Bạn có chắc chắn muốn xóa xử lý này", "function.handle.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> lý", "function.handle.modal.title-info": "<PERSON>em chi ti<PERSON><PERSON> x<PERSON> lý", "function.handle.modal.title-edit": "<PERSON><PERSON><PERSON><PERSON> lý", "function.handle.page.title": "<PERSON><PERSON> s<PERSON>ch xử lý", "menu.title": "<PERSON><PERSON><PERSON> danh", "function.title.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.title.table.code": "<PERSON><PERSON> chức danh", "function.title.table.name": "<PERSON><PERSON><PERSON> danh", "function.title.modal.form.code": "<PERSON><PERSON> chức danh ", "function.title.modal.form.code.required": "<PERSON><PERSON> chức danh không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.code.place-holder": "<PERSON><PERSON> chức danh ", "function.title.modal.form.name": "<PERSON><PERSON><PERSON> ch<PERSON> danh ", "function.title.modal.form.name.required": "<PERSON><PERSON><PERSON> chức danh không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> ch<PERSON> danh ", "function.title.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.title.confirm-delete.content": "Bạn có chắc chắn muốn xóa chức danh này", "function.title.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON> danh", "function.title.modal.title-info": "<PERSON>em chi ti<PERSON><PERSON> chức danh", "function.title.modal.title-edit": "<PERSON><PERSON><PERSON> ch<PERSON> danh", "function.title.page.title": "<PERSON><PERSON> s<PERSON>ch chức danh", "menu.rating": "<PERSON><PERSON><PERSON> lo<PERSON> r<PERSON>n l<PERSON>", "function.rating.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.rating.table.name": "<PERSON><PERSON><PERSON> lo<PERSON> r<PERSON>n l<PERSON>", "function.rating.table.tu-diem": "<PERSON><PERSON> điểm", "function.rating.table.den-diem": "<PERSON><PERSON><PERSON> đi<PERSON>", "function.rating.table.he-so": "<PERSON><PERSON> s<PERSON>", "function.rating.table.name-en": "<PERSON><PERSON><PERSON> lo<PERSON>i rèn luy<PERSON>n English", "function.rating.modal.form.name": "<PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON>i rèn luy<PERSON>n ", "function.rating.modal.form.name.place-holder": "<PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON>i rèn luy<PERSON>n ", "function.rating.modal.form.tu-diem": "<PERSON><PERSON> điểm ", "function.rating.modal.form.tu-diem.place-holder": "<PERSON><PERSON> điểm", "function.rating.modal.form.den-diem": "<PERSON><PERSON><PERSON> đi<PERSON>", "function.rating.modal.form.den-diem.place-holder": "<PERSON><PERSON><PERSON> đi<PERSON> ", "function.rating.modal.form.he-so": "<PERSON><PERSON> s<PERSON>", "function.rating.modal.form.he-so.place-holder": "<PERSON><PERSON> s<PERSON>", "function.rating.modal.form.name-En": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> lo<PERSON>i rèn luyện <PERSON> ", "function.rating.modal.form.name-En.place-holder": "<PERSON><PERSON><PERSON> xếp loại rèn luyện bằng tiếng anh ", "function.rating.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.rating.confirm-delete.content": "Bạn có chắc chắn muốn xóa xếp loại rèn luyện này", "function.rating.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> x<PERSON><PERSON> lo<PERSON> rèn l<PERSON>n", "function.rating.modal.title-info": "<PERSON>em chi tiết xếp lo<PERSON>i rèn luy<PERSON>n", "function.rating.modal.title-edit": "<PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON>i rèn l<PERSON>n", "function.rating.page.title": "<PERSON><PERSON> s<PERSON>ch x<PERSON>p lo<PERSON> r<PERSON>n l<PERSON>n", "menu.type-of-training": "<PERSON><PERSON><PERSON> r<PERSON> l<PERSON>", "function.type-of-training.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.type-of-training.table.ten-cap-rl": "Ten cấp rèn luy<PERSON>n", "function.type-of-training.table.ky-hieu": "<PERSON><PERSON>", "function.type-of-training.table.name": "<PERSON><PERSON><PERSON>", "function.type-of-training.table.diem": "<PERSON><PERSON><PERSON><PERSON>", "function.type-of-training.table.diem-tru": "<PERSON><PERSON><PERSON><PERSON> trừ", "function.type-of-training.table.hoc-tap": "<PERSON><PERSON><PERSON>", "function.type-of-training.table.tinh-diem": "<PERSON><PERSON><PERSON> điể<PERSON>", "function.type-of-training.table.hien-thi": "<PERSON><PERSON><PERSON> thị", "function.type-of-training.modal.form.ten-cap-rl": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> rèn l<PERSON>n", "function.type-of-training.modal.form.ten-cap-rl.required": "<PERSON><PERSON><PERSON> cấp rèn luyện không đư<PERSON><PERSON> để trống", "function.type-of-training.modal.form.ten-cap-rl.place-holder": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> rèn l<PERSON>n", "function.type-of-training.modal.form.ky-hieu": "<PERSON><PERSON> ", "function.type-of-training.modal.form.ky-hieu.required": "<PERSON><PERSON> hiệu không đư<PERSON><PERSON> để trống", "function.type-of-training.modal.form.ky-hieu.place-holder": "<PERSON><PERSON>", "function.type-of-training.modal.form.name": "<PERSON><PERSON><PERSON>  ", "function.type-of-training.modal.form.name.required": "<PERSON><PERSON><PERSON> lo<PERSON>i không đư<PERSON>c để trống", "function.type-of-training.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> lo<PERSON>i rèn luy<PERSON>n", "function.type-of-training.modal.form.diem": "<PERSON><PERSON><PERSON><PERSON> ", "function.type-of-training.modal.form.diem.required": "<PERSON><PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "function.type-of-training.modal.form.diem.place-holder": "<PERSON><PERSON><PERSON><PERSON>", "function.type-of-training.modal.form.diem-tru": "<PERSON><PERSON><PERSON><PERSON> trừ", "function.type-of-training.modal.form.diem-tru.place-holder": "<PERSON><PERSON><PERSON><PERSON> trừ", "function.type-of-training.modal.form.hoc-tap": "<PERSON><PERSON><PERSON>", "function.type-of-training.modal.form.hoc-tap.place-holder": "<PERSON><PERSON><PERSON>", "function.type-of-training.modal.form.tinh-diem": "<PERSON><PERSON><PERSON> điể<PERSON> ", "function.type-of-training.modal.form.tinh-diem.place-holder": "<PERSON><PERSON><PERSON> điể<PERSON>", "function.type-of-training.modal.form.hien-thi": "<PERSON><PERSON><PERSON> thị", "function.type-of-training.modal.form.hien-thi.place-holder": "<PERSON><PERSON><PERSON> thị", "function.type-of-training.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.type-of-training.confirm-delete.content": "Bạn có chắc chắn muốn xóa loại rèn luyện này", "function.type-of-training.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON> rèn l<PERSON>n", "function.type-of-training.modal.title-info": "<PERSON>em chi tiết lo<PERSON>i rèn luy<PERSON>n", "function.type-of-training.modal.title-edit": "<PERSON><PERSON><PERSON> lo<PERSON>i rèn l<PERSON>n", "function.type-of-training.page.title": "<PERSON><PERSON> s<PERSON>ch lo<PERSON>i rèn l<PERSON>", "menu.loai-thanh-phan-diem": "<PERSON><PERSON><PERSON> thành phần điểm", "function.loai-thanh-phan-diem.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm tên thành phần", "function.loai-thanh-phan-diem.table.stt": "Stt", "function.loai-thanh-phan-diem.table.stt.place-holder": "<PERSON><PERSON><PERSON><PERSON> số thứ tự", "function.loai-thanh-phan-diem.table.stt.required": "<PERSON><PERSON> thứ tự không được để trống", "function.loai-thanh-phan-diem.table.ten-thanh-phan": "<PERSON><PERSON><PERSON> thành ph<PERSON>n", "function.loai-thanh-phan-diem.table.ten-thanh-phan.place-holder": "<PERSON><PERSON><PERSON><PERSON> tên thành phần", "function.loai-thanh-phan-diem.table.ten-thanh-phan.required": "<PERSON>ên thành phần không được để trống", "function.loai-thanh-phan-diem.table.ky-hieu": "<PERSON><PERSON>", "function.loai-thanh-phan-diem.table.ky-hieu.place-holder": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>u", "function.loai-thanh-phan-diem.table.ky-hieu.required": "<PERSON><PERSON> hiệu không đư<PERSON><PERSON> để trống", "function.loai-thanh-phan-diem.table.tyLe": "Tỷ lệ", "function.loai-thanh-phan-diem.table.tyLe.place-holder": "<PERSON><PERSON><PERSON><PERSON> tỷ lệ", "function.loai-thanh-phan-diem.table.tyLe.required": "Tỷ lệ không đư<PERSON><PERSON> để trống", "function.loai-thanh-phan-diem.table.chon-mac-dinh": "<PERSON><PERSON>n mặc định", "function.loai-thanh-phan-diem.table.chon-mac-dinh.place-holder": "<PERSON><PERSON>n mặc định", "function.loai-thanh-phan-diem.table.chon-mac-dinh.required": "<PERSON><PERSON><PERSON> chọn mặc định", "function.loai-thanh-phan-diem.table.chuyen-can": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>n", "function.loai-thanh-phan-diem.table.chuyen-can.place-holder": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON> cần", "function.loai-thanh-phan-diem.table.chuyen-can.required": "<PERSON><PERSON><PERSON><PERSON> cần không đ<PERSON><PERSON><PERSON> để trống", "function.loai-thanh-phan-diem.table.nhom-thanh-phan": "<PERSON><PERSON><PERSON><PERSON> thành phần", "function.loai-thanh-phan-diem.table.nhom-thanh-phan.place-holder": "<PERSON><PERSON><PERSON><PERSON> nhóm thành phần", "function.loai-thanh-phan-diem.table.nhom-thanh-phan.required": "<PERSON><PERSON><PERSON><PERSON> thành phần không đư<PERSON><PERSON> để trống", "function.loai-thanh-phan-diem.table.ty-le-mhom": "Tỷ lệ nhóm", "function.loai-thanh-phan-diem.table.ty-le-mhom.place-holder": "<PERSON><PERSON>ậ<PERSON> tỷ lệ nhóm", "function.loai-thanh-phan-diem.table.ty-le-mhom.required": "Tỷ lệ nhóm không được để trống", "function.loai-thanh-phan-diem.table.hien-thi": "<PERSON><PERSON>", "function.loai-thanh-phan-diem.table.hien-thi.place-holder": "<PERSON><PERSON><PERSON><PERSON> ký hi<PERSON>u nh<PERSON>", "function.loai-thanh-phan-diem.table.hien-thi.required": "<PERSON><PERSON> hiệu nhóm không được để trống", "function.loai-thanh-phan-diem.table.thuc-hanh": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh", "function.loai-thanh-phan-diem.table.thuc-hanh.place-holder": "<PERSON><PERSON><PERSON><PERSON> th<PERSON><PERSON> h<PERSON>nh", "function.loai-thanh-phan-diem.table.thuc-hanh.required": "<PERSON><PERSON><PERSON><PERSON> hành không đư<PERSON><PERSON> để trống", "function.loai-thanh-phan-diem.confirm-delete.content": "Bạn có chắc chắn muốn xóa loại thành phần điểm", "function.loai-thanh-phan-diem.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> xếp loại thành phần điểm", "function.loai-thanh-phan-diem.modal.title-info": "<PERSON>em chi tiết xếp loại thành phần điểm", "function.loai-thanh-phan-diem.modal.title-edit": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> loại thành phần điểm", "function.loai-thanh-phan-diem.page.title": "<PERSON><PERSON> s<PERSON>ch xếp lo<PERSON>i thành phần điểm", "menu.xep-loai-hoc-tap-thang-10": "<PERSON><PERSON><PERSON> lo<PERSON> học tập thang 10", "function.xep-loai-hoc-tap-thang-10.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm theo xếp lo<PERSON>i", "function.xep-loai-hoc-tap-thang-10.table.xep-loai": "<PERSON><PERSON><PERSON>", "function.xep-loai-hoc-tap-thang-10.table.xep-loai.place-holder": "<PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON>", "function.xep-loai-hoc-tap-thang-10.table.xep-loai.required": "<PERSON><PERSON><PERSON> lo<PERSON> không đư<PERSON><PERSON> để trống", "function.xep-loai-hoc-tap-thang-10.table.xep-loai-en": "<PERSON><PERSON><PERSON> (English)", "function.xep-loai-hoc-tap-thang-10.table.xep-loai-en.place-holder": "<PERSON><PERSON><PERSON><PERSON> (English)", "function.xep-loai-hoc-tap-thang-10.table.xep-loai-en.required": "<PERSON><PERSON><PERSON> (English) không được để trống", "function.xep-loai-hoc-tap-thang-10.table.tu-diem": "<PERSON><PERSON> điểm", "function.xep-loai-hoc-tap-thang-10.table.tu-diem.place-holder": "<PERSON><PERSON><PERSON><PERSON> từ điểm", "function.xep-loai-hoc-tap-thang-10.table.tu-diem.required": "Từ điểm không đư<PERSON><PERSON> để trống", "function.xep-loai-hoc-tap-thang-10.table.den-diem": "<PERSON><PERSON><PERSON> đi<PERSON>", "function.xep-loai-hoc-tap-thang-10.table.den-diem.place-holder": "<PERSON><PERSON><PERSON><PERSON> đến điểm", "function.xep-loai-hoc-tap-thang-10.table.den-diem.required": "<PERSON><PERSON><PERSON> điểm không đ<PERSON><PERSON><PERSON> để trống", "function.xep-loai-hoc-tap-thang-10.table.ma-xep-loai": "<PERSON><PERSON> x<PERSON> lo<PERSON>", "function.xep-loai-hoc-tap-thang-10.table.ma-xep-loai.place-holder": "<PERSON><PERSON><PERSON><PERSON> mã xếp lo<PERSON>i", "function.xep-loai-hoc-tap-thang-10.table.ma-xep-loai.required": "mã xếp lo<PERSON>i không đư<PERSON><PERSON> để trống", "function.xep-loai-hoc-tap-thang-10.table.ten-he": "<PERSON><PERSON><PERSON>", "function.xep-loai-hoc-tap-thang-10.table.ten-he.place-holder": "<PERSON><PERSON><PERSON>", "function.xep-loai-hoc-tap-thang-10.table.ten-he.required": "<PERSON><PERSON> không đư<PERSON><PERSON> để trống", "function.xep-loai-hoc-tap-thang-10.confirm-delete.content": "Bạn có chắc chắn muốn xóa xếp lo<PERSON>i", "function.xep-loai-hoc-tap-thang-10.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> x<PERSON><PERSON> lo<PERSON> học tập thang 10", "function.xep-loai-hoc-tap-thang-10.modal.title-info": "<PERSON>em chi ti<PERSON>t xếp lo<PERSON>i học tập thang 10", "function.xep-loai-hoc-tap-thang-10.modal.title-edit": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> lo<PERSON>i học tập thang 10", "function.xep-loai-hoc-tap-thang-10.page.title": "<PERSON><PERSON> s<PERSON>ch xếp lo<PERSON> học tập thang 10", "menu.xep-hang-hoc-luc": "<PERSON><PERSON><PERSON> h<PERSON> h<PERSON> lực", "function.xep-hang-hoc-luc.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm theo xếp hạng", "function.xep-hang-hoc-luc.table.xep-hang": "<PERSON><PERSON><PERSON> h<PERSON>", "function.xep-hang-hoc-luc.table.xep-hang.place-holder": "<PERSON><PERSON><PERSON><PERSON><PERSON> hạng", "function.xep-hang-hoc-luc.table.xep-hang.required": "<PERSON><PERSON><PERSON> hạng không đư<PERSON><PERSON> để trống", "function.xep-hang-hoc-luc.table.xep-hang-en": "<PERSON><PERSON><PERSON> (English)", "function.xep-hang-hoc-luc.table.xep-hang-en.place-holder": "<PERSON><PERSON><PERSON><PERSON> (English)", "function.xep-hang-hoc-luc.table.xep-hang-en.required": "<PERSON><PERSON><PERSON> (English) không được để trống", "function.xep-hang-hoc-luc.table.tu-diem": "<PERSON><PERSON> điểm", "function.xep-hang-hoc-luc.table.tu-diem.place-holder": "<PERSON><PERSON><PERSON><PERSON> từ điểm", "function.xep-hang-hoc-luc.table.tu-diem.required": "Từ điểm không đư<PERSON><PERSON> để trống", "function.xep-hang-hoc-luc.table.den-diem": "<PERSON><PERSON><PERSON> đi<PERSON>", "function.xep-hang-hoc-luc.table.den-diem.place-holder": "<PERSON><PERSON><PERSON><PERSON> đến điểm", "function.xep-hang-hoc-luc.table.den-diem.required": "<PERSON><PERSON><PERSON> điểm không đ<PERSON><PERSON><PERSON> để trống", "function.xep-hang-hoc-luc.table.ma-xep-loai": "<PERSON><PERSON> x<PERSON> lo<PERSON>", "function.xep-hang-hoc-luc.table.ma-xep-loai.place-holder": "<PERSON><PERSON><PERSON><PERSON> mã xếp lo<PERSON>i", "function.xep-hang-hoc-luc.table.ma-xep-loai.required": "mã xếp lo<PERSON>i không đư<PERSON><PERSON> để trống", "function.xep-hang-hoc-luc.table.ten-he": "<PERSON><PERSON><PERSON>", "function.xep-hang-hoc-luc.table.ten-he.place-holder": "<PERSON><PERSON><PERSON>", "function.xep-hang-hoc-luc.table.ten-he.required": "<PERSON><PERSON> không đư<PERSON><PERSON> để trống", "function.xep-hang-hoc-luc.confirm-delete.content": "Bạn có chắc chắn muốn xóa xếp hạng", "function.xep-hang-hoc-luc.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> x<PERSON><PERSON> hạng h<PERSON> lực", "function.xep-hang-hoc-luc.modal.title-info": "<PERSON>em chi ti<PERSON>t xế<PERSON> hạng h<PERSON> lực", "function.xep-hang-hoc-luc.modal.title-edit": "<PERSON><PERSON><PERSON><PERSON><PERSON> hạng h<PERSON> lực", "function.xep-hang-hoc-luc.page.title": "<PERSON><PERSON> s<PERSON>ch x<PERSON><PERSON> hạng h<PERSON> lực", "menu.xep-hang-nam-dao-tao": "<PERSON><PERSON><PERSON> hạng năm đào tạo", "function.xep-hang-nam-dao-tao.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm theo năm thứ", "function.xep-hang-nam-dao-tao.table.nam-thu": "<PERSON><PERSON><PERSON>", "function.xep-hang-nam-dao-tao.table.nam-thu.place-holder": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> thứ", "function.xep-hang-nam-dao-tao.table.nam-thu.required": "<PERSON><PERSON><PERSON> thứ không đư<PERSON><PERSON> để trống", "function.xep-hang-nam-dao-tao.table.tu-tin-chi": "Từ tín chỉ", "function.xep-hang-nam-dao-tao.table.tu-tin-chi.place-holder": "<PERSON><PERSON><PERSON><PERSON> từ tín chỉ", "function.xep-hang-nam-dao-tao.table.tu-tin-chi.required": "Từ tín chỉ không được để trống", "function.xep-hang-nam-dao-tao.table.den-tin-chi": "<PERSON><PERSON><PERSON> tín chỉ", "function.xep-hang-nam-dao-tao.table.den-tin-chi.place-holder": "<PERSON><PERSON><PERSON><PERSON> đến tín chỉ", "function.xep-hang-nam-dao-tao.table.den-tin-chi.required": "<PERSON><PERSON><PERSON> tín chỉ không được để trống", "function.xep-hang-nam-dao-tao.table.nam-thu-en": "<PERSON><PERSON><PERSON> (English)", "function.xep-hang-nam-dao-tao.table.nam-thu-en.place-holder": "<PERSON><PERSON><PERSON><PERSON> (English)", "function.xep-hang-nam-dao-tao.table.nam-thu-en.required": "<PERSON><PERSON><PERSON> (English) không được để trống", "function.xep-hang-nam-dao-tao.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> xếp hạng năm đào tạo", "function.xep-hang-nam-dao-tao.modal.title-info": "<PERSON>em chi tiết xếp hạng năm đào tạo", "function.xep-hang-nam-dao-tao.modal.title-edit": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> hạng năm đào tạo", "function.xep-hang-nam-dao-tao.page.title": "<PERSON><PERSON> s<PERSON>ch xếp hạng năm đào tạo", "menu.xep-loai-hoc-luc-thang-4": "<PERSON><PERSON><PERSON> lo<PERSON> học tập thang 4", "function.xep-loai-hoc-luc-thang-4.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm theo xếp lo<PERSON>i", "function.xep-loai-hoc-luc-thang-4.table.xep-loai": "<PERSON><PERSON><PERSON>", "function.xep-loai-hoc-luc-thang-4.table.xep-loai.place-holder": "<PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON>", "function.xep-loai-hoc-luc-thang-4.table.xep-loai.required": "<PERSON><PERSON><PERSON> lo<PERSON> không đư<PERSON><PERSON> để trống", "function.xep-loai-hoc-luc-thang-4.table.ma-xep-loai": "<PERSON><PERSON> x<PERSON> lo<PERSON>", "function.xep-loai-hoc-luc-thang-4.table.ma-xep-loai.place-holder": "<PERSON><PERSON><PERSON><PERSON> mã xếp lo<PERSON>i", "function.xep-loai-hoc-luc-thang-4.table.ma-xep-loai.required": "<PERSON><PERSON> xếp lo<PERSON>i không đư<PERSON><PERSON> để trống", "function.xep-loai-hoc-luc-thang-4.table.tu-diem": "<PERSON><PERSON> điểm", "function.xep-loai-hoc-luc-thang-4.table.tu-diem.place-holder": "<PERSON><PERSON><PERSON><PERSON> từ điểm", "function.xep-loai-hoc-luc-thang-4.table.tu-diem.required": "Từ điểm không đư<PERSON><PERSON> để trống", "function.xep-loai-hoc-luc-thang-4.table.den-diem": "<PERSON><PERSON><PERSON> đi<PERSON>", "function.xep-loai-hoc-luc-thang-4.table.den-diem.place-holder": "<PERSON><PERSON><PERSON><PERSON> đến điểm", "function.xep-loai-hoc-luc-thang-4.table.den-diem.required": "<PERSON><PERSON><PERSON> điểm không đ<PERSON><PERSON><PERSON> để trống", "function.xep-loai-hoc-luc-thang-4.table.xep-loai-en": "<PERSON><PERSON><PERSON> (English)", "function.xep-loai-hoc-luc-thang-4.table.xep-loai-en.place-holder": "<PERSON><PERSON><PERSON><PERSON> (English)", "function.xep-loai-hoc-luc-thang-4.table.xep-loai-en.required": "<PERSON><PERSON><PERSON> (English) không được để trống", "function.xep-loai-hoc-luc-thang-4.table.ten-he": "<PERSON><PERSON><PERSON>", "function.xep-loai-hoc-luc-thang-4.table.ten-he.place-holder": "<PERSON><PERSON><PERSON><PERSON> tên hệ", "function.xep-loai-hoc-luc-thang-4.table.ten-he.required": "<PERSON><PERSON><PERSON> hệ không đ<PERSON><PERSON><PERSON> để trống", "function.xep-loai-hoc-luc-thang-4.confirm-delete.content": "Bạn có chắc chắn muốn xóa xếp lo<PERSON>i", "function.xep-loai-hoc-luc-thang-4.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> x<PERSON><PERSON> lo<PERSON> học tập thang 4", "function.xep-loai-hoc-luc-thang-4.modal.title-info": "<PERSON><PERSON> chi ti<PERSON><PERSON> xế<PERSON> lo<PERSON>i học tập thang 4", "function.xep-loai-hoc-luc-thang-4.modal.title-edit": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> lo<PERSON> học tập thang 4", "function.xep-loai-hoc-luc-thang-4.page.title": "<PERSON><PERSON> s<PERSON>ch xế<PERSON> lo<PERSON> học tập thang 4", "menu.xep-loai-tot-nghiep-thang-4": "<PERSON>ếp lo<PERSON>i tốt nghi<PERSON> thang 4", "function.xep-loai-tot-nghiep-thang-4.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm theo xếp hạng", "function.xep-loai-tot-nghiep-thang-4.table.xep-hang": "<PERSON><PERSON><PERSON> h<PERSON>", "function.xep-loai-tot-nghiep-thang-4.table.xep-hang.place-holder": "<PERSON><PERSON><PERSON><PERSON><PERSON> hạng", "function.xep-loai-tot-nghiep-thang-4.table.xep-hang.required": "<PERSON><PERSON><PERSON> hạng không đư<PERSON><PERSON> để trống", "function.xep-loai-tot-nghiep-thang-4.table.ma-xep-hang": "<PERSON><PERSON> xế<PERSON> hạng", "function.xep-loai-tot-nghiep-thang-4.table.ma-xep-hang.place-holder": "<PERSON><PERSON><PERSON><PERSON> mã xếp hạng", "function.xep-loai-tot-nghiep-thang-4.table.ma-xep-hang.required": "<PERSON><PERSON> xếp hạng không đư<PERSON><PERSON> để trống", "function.xep-loai-tot-nghiep-thang-4.table.tu-diem": "<PERSON><PERSON> điểm", "function.xep-loai-tot-nghiep-thang-4.table.tu-diem.place-holder": "<PERSON><PERSON><PERSON><PERSON> từ điểm", "function.xep-loai-tot-nghiep-thang-4.table.tu-diem.required": "Từ điểm không đư<PERSON><PERSON> để trống", "function.xep-loai-tot-nghiep-thang-4.table.den-diem": "<PERSON><PERSON><PERSON> đi<PERSON>", "function.xep-loai-tot-nghiep-thang-4.table.den-diem.place-holder": "<PERSON><PERSON><PERSON><PERSON> đến điểm", "function.xep-loai-tot-nghiep-thang-4.table.den-diem.required": "<PERSON><PERSON><PERSON> điểm không đ<PERSON><PERSON><PERSON> để trống", "function.xep-loai-tot-nghiep-thang-4.table.tu-diem-thang-10": "<PERSON><PERSON> điểm thang 10", "function.xep-loai-tot-nghiep-thang-4.table.tu-diem-thang-10.place-holder": "<PERSON><PERSON><PERSON><PERSON> từ điểm thang 10", "function.xep-loai-tot-nghiep-thang-4.table.tu-diem-thang-10.required": "Từ điểm thang 10 không đư<PERSON>c để trống", "function.xep-loai-tot-nghiep-thang-4.table.den-diem-thang-10": "<PERSON><PERSON><PERSON> đi<PERSON> thang 10", "function.xep-loai-tot-nghiep-thang-4.table.den-diem-thang-10.place-holder": "<PERSON><PERSON><PERSON><PERSON> đến điểm thang 10", "function.xep-loai-tot-nghiep-thang-4.table.den-diem-thang-10.required": "<PERSON><PERSON><PERSON> điểm thang 10 không đư<PERSON><PERSON> để trống", "function.xep-loai-tot-nghiep-thang-4.table.xep-hang-en": "<PERSON><PERSON><PERSON> (English)", "function.xep-loai-tot-nghiep-thang-4.table.xep-hang-en.place-holder": "<PERSON><PERSON><PERSON><PERSON> (English)", "function.xep-loai-tot-nghiep-thang-4.table.xep-hang-en.required": "<PERSON><PERSON><PERSON> (English) không được để trống", "function.xep-loai-tot-nghiep-thang-4.table.ten-he": "<PERSON><PERSON><PERSON>", "function.xep-loai-tot-nghiep-thang-4.table.ten-he.place-holder": "<PERSON><PERSON><PERSON><PERSON> tên hệ", "function.xep-loai-tot-nghiep-thang-4.table.ten-he.required": "<PERSON><PERSON><PERSON> hệ không đ<PERSON><PERSON><PERSON> để trống", "function.xep-loai-tot-nghiep-thang-4.confirm-delete.content": "Bạn có chắc chắn muốn xóa xếp hạng", "function.xep-loai-tot-nghiep-thang-4.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> x<PERSON><PERSON> lo<PERSON>i tốt nghi<PERSON>p thang 4", "function.xep-loai-tot-nghiep-thang-4.modal.title-info": "<PERSON>em chi tiết xếp loại tốt nghiệp thang 4", "function.xep-loai-tot-nghiep-thang-4.modal.title-edit": "<PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON>i tốt nghi<PERSON> thang 4", "function.xep-loai-tot-nghiep-thang-4.page.title": "<PERSON>ếp lo<PERSON>i tốt nghi<PERSON> thang 4", "menu.xep-loai-tot-nghiep-thang-10": "XL tốt nghi<PERSON> thang 10", "function.xep-loai-tot-nghiep-thang-10.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm theo xếp hạng", "function.xep-loai-tot-nghiep-thang-10.table.xep-hang": "<PERSON><PERSON><PERSON> h<PERSON>", "function.xep-loai-tot-nghiep-thang-10.table.xep-hang.place-holder": "<PERSON><PERSON><PERSON><PERSON><PERSON> hạng", "function.xep-loai-tot-nghiep-thang-10.table.xep-hang.required": "<PERSON><PERSON><PERSON> hạng không đư<PERSON><PERSON> để trống", "function.xep-loai-tot-nghiep-thang-10.table.ma-xep-hang": "<PERSON><PERSON> xế<PERSON> hạng", "function.xep-loai-tot-nghiep-thang-10.table.ma-xep-hang.place-holder": "<PERSON><PERSON><PERSON><PERSON> mã xếp hạng", "function.xep-loai-tot-nghiep-thang-10.table.ma-xep-hang.required": "<PERSON><PERSON> xếp hạng không đư<PERSON><PERSON> để trống", "function.xep-loai-tot-nghiep-thang-10.table.tu-diem": "<PERSON><PERSON> điểm", "function.xep-loai-tot-nghiep-thang-10.table.tu-diem.place-holder": "<PERSON><PERSON><PERSON><PERSON> từ điểm", "function.xep-loai-tot-nghiep-thang-10.table.tu-diem.required": "Từ điểm không đư<PERSON><PERSON> để trống", "function.xep-loai-tot-nghiep-thang-10.table.den-diem": "<PERSON><PERSON><PERSON> đi<PERSON>", "function.xep-loai-tot-nghiep-thang-10.table.den-diem.place-holder": "<PERSON><PERSON><PERSON><PERSON> đến điểm", "function.xep-loai-tot-nghiep-thang-10.table.den-diem.required": "<PERSON><PERSON><PERSON> điểm không đ<PERSON><PERSON><PERSON> để trống", "function.xep-loai-tot-nghiep-thang-10.table.xep-hang-en": "<PERSON><PERSON><PERSON> (English)", "function.xep-loai-tot-nghiep-thang-10.table.xep-hang-en.place-holder": "<PERSON><PERSON><PERSON><PERSON> (English)", "function.xep-loai-tot-nghiep-thang-10.table.xep-hang-en.required": "<PERSON><PERSON><PERSON> (English) không được để trống", "function.xep-loai-tot-nghiep-thang-10.table.ten-he": "<PERSON><PERSON><PERSON>", "function.xep-loai-tot-nghiep-thang-10.table.ten-he.place-holder": "<PERSON><PERSON><PERSON><PERSON> tên hệ", "function.xep-loai-tot-nghiep-thang-10.table.ten-he.required": "<PERSON><PERSON><PERSON> hệ không đ<PERSON><PERSON><PERSON> để trống", "function.xep-loai-tot-nghiep-thang-10.confirm-delete.content": "Bạn có chắc chắn muốn xóa xếp hạng", "function.xep-loai-tot-nghiep-thang-10.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> x<PERSON><PERSON> lo<PERSON>i tốt nghiệp thang 10", "function.xep-loai-tot-nghiep-thang-10.modal.title-info": "<PERSON>em chi tiết xếp loại tốt nghiệp thang 10", "function.xep-loai-tot-nghiep-thang-10.modal.title-edit": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> lo<PERSON>i tốt nghiệp thang 10", "function.xep-loai-tot-nghiep-thang-10.page.title": "XL tốt nghi<PERSON> thang 10", "menu.loai-chung-chi": "<PERSON><PERSON><PERSON> chứng chỉ", "function.loai-chung-chi.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm theo loại chứng chỉ", "function.loai-chung-chi.table.ky-hieu": "<PERSON><PERSON>", "function.loai-chung-chi.table.ky-hieu.place-holder": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>u", "function.loai-chung-chi.table.ky-hieu.required": "ký hiệu không đư<PERSON><PERSON> để trống", "function.loai-chung-chi.table.loai-chung-chi": "<PERSON><PERSON><PERSON> chứng chỉ", "function.loai-chung-chi.table.loai-chung-chi.place-holder": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON> chứng chỉ", "function.loai-chung-chi.table.loai-chung-chi.required": "lo<PERSON>i chứng chỉ không được để trống", "function.loai-chung-chi.table.nhom-chung-chi": "<PERSON><PERSON><PERSON><PERSON> chứng chỉ", "function.loai-chung-chi.table.nhom-chung-chi.place-holder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON> chứng chỉ", "function.loai-chung-chi.table.nhom-chung-chi.required": "nh<PERSON><PERSON> chứng chỉ không được để trống", "function.loai-chung-chi.table.cao-do-chung-chi": "<PERSON><PERSON><PERSON> độ chứng chỉ", "function.loai-chung-chi.table.cao-do-chung-chi.place-holder": "<PERSON><PERSON><PERSON><PERSON> cấp độ chứng chỉ", "function.loai-chung-chi.table.cao-do-chung-chi.required": "cấp độ chứng chỉ không được để trống", "function.loai-chung-chi.confirm-delete.content": "Bạn có chắc chắn muốn xóa chứng chỉ", "function.loai-chung-chi.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>ng chỉ", "function.loai-chung-chi.modal.title-info": "<PERSON>em chi tiết loại chứng chỉ", "function.loai-chung-chi.modal.title-edit": "<PERSON><PERSON><PERSON> lo<PERSON>i chứng chỉ", "function.loai-chung-chi.page.title": "<PERSON><PERSON><PERSON> chứng chỉ", "menu.xep-loai-chung-chi": " <PERSON><PERSON><PERSON> lo<PERSON>i chứng chỉ", "function.xep-loai-chung-chi.confirm-delete.content": "Bạn có chắc chắn muốn xóa chứng chỉ", "function.xep-loai-chung-chi.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>p lo<PERSON>i chứng chỉ", "function.xep-loai-chung-chi.modal.title-info": "<PERSON>em chi tiết xếp loại chứng chỉ", "function.xep-loai-chung-chi.modal.title-edit": "<PERSON><PERSON><PERSON> x<PERSON>p lo<PERSON>i chứng chỉ", "function.xep-loai-chung-chi.page.title": "<PERSON><PERSON><PERSON> lo<PERSON>i chứng chỉ", "menu.nhom-chung-chi": "<PERSON><PERSON><PERSON><PERSON> chứng chỉ", "function.nhom-chung-chi.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm theo nhóm chứng chỉ", "function.nhom-chung-chi.table.ky-hieu": "<PERSON><PERSON>", "function.nhom-chung-chi.table.ky-hieu.place-holder": "<PERSON><PERSON><PERSON><PERSON> ký hi<PERSON>u nh<PERSON>", "function.nhom-chung-chi.table.ky-hieu.required": "ký hiệu nhóm không được để trống", "function.nhom-chung-chi.table.nhom-chung-chi": "<PERSON><PERSON><PERSON><PERSON> chứng chỉ", "function.nhom-chung-chi.table.nhom-chung-chi.place-holder": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON> chứng chỉ", "function.nhom-chung-chi.table.nhom-chung-chi.required": "nh<PERSON><PERSON> chứng chỉ không được để trống", "function.nhom-chung-chi.confirm-delete.content": "Bạn có chắc chắn muốn xóa chứng chỉ", "function.nhom-chung-chi.modal.title-add": "<PERSON>h<PERSON><PERSON> nhóm chứng chỉ", "function.nhom-chung-chi.modal.title-info": "<PERSON>em chi tiết nhóm chứng chỉ", "function.nhom-chung-chi.modal.title-edit": "<PERSON><PERSON><PERSON> nh<PERSON>m chứng chỉ", "function.nhom-chung-chi.page.title": "<PERSON><PERSON><PERSON><PERSON> chứng chỉ", "menu.title-loai-giay-to": "<PERSON><PERSON><PERSON> gi<PERSON>y tờ", "function.title.table.name.id-giay-to": "<PERSON>d g<PERSON><PERSON><PERSON> tờ", "function.title.table.name.ma-giay-to": "<PERSON><PERSON> gi<PERSON>y tờ", "function.title.table.name.ten-giay-to": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> tờ", "function.title.table.name.stt-giay-to": "Stt", "function.title.table.name.giay-to-bat-buoc": "<PERSON><PERSON><PERSON> b<PERSON>", "function.title.table.name.giay-to-mac-dinh": "Mặc định", "function.title.table.name.giay-to-nhom": "Nhóm", "function.title.table.name.giay-to-ghi-chu": "<PERSON><PERSON><PERSON>", "function.title.table.name.giay-to-id-he": "<PERSON><PERSON><PERSON>", "function.title.table.name.giay-to-id-phong": "<PERSON><PERSON><PERSON> ph<PERSON>ng", "function.title.table.name.giay-to-tuyen-sinh": "<PERSON><PERSON><PERSON><PERSON>h", "function.title.modal.form.ma-giay-to": "<PERSON><PERSON> gi<PERSON>y tờ ", "function.title.modal.form.ma-giay-to.required": "<PERSON><PERSON> giấy tờ không được để trống", "function.title.modal.form.ma-giay-to.place-holder": "<PERSON><PERSON> gi<PERSON>y tờ ", "function.title.modal.form.id-giay-to": "<PERSON>d g<PERSON><PERSON><PERSON> tờ ", "function.title.modal.form.id-giay-to.required": "Id g<PERSON><PERSON><PERSON> tờ không đư<PERSON>c để trống", "function.title.modal.form.id-giay-to.place-holder": "<PERSON>d g<PERSON><PERSON><PERSON> tờ ", "function.title.modal.form.ten-giay-to": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> tờ ", "function.title.modal.form.ten-giay-to.required": "<PERSON><PERSON><PERSON> gi<PERSON>y tờ không được để trống", "function.title.modal.form.ten-giay-to.place-holder": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> tờ ", "function.title.modal.form.stt-giay-to": "<PERSON>t gi<PERSON>y tờ ", "function.title.modal.form.stt-giay-to.required": "<PERSON>t gi<PERSON>y tờ không được để trống", "function.title.modal.form.stt-giay-to.place-holder": "<PERSON>t gi<PERSON>y tờ ", "function.type-of-training.modal.form.bat-buoc": "<PERSON><PERSON><PERSON> b<PERSON>", "function.type-of-training.modal.form.mac-dinh": "Mặc định", "function.title.modal.form.nhom-giay-to": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON>y tờ ", "function.title.modal.form.nhom-giay-to.required": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON>y tờ không được để trống", "function.title.modal.form.nhom-giay-to.place-holder": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON>y tờ ", "function.title.modal.form.ghi-chu-giay-to": "<PERSON><PERSON> chú gi<PERSON>y tờ ", "function.title.modal.form.ghi-chu-giay-to.required": "<PERSON><PERSON> chú giấy tờ không được để trống", "function.title.modal.form.ghi-chu-giay-to.place-holder": "<PERSON><PERSON> chú gi<PERSON>y tờ ", "function.title.modal.form.id-he-giay-to": "<PERSON><PERSON><PERSON> ", "function.title.modal.form.id-he-giay-to.required": "<PERSON><PERSON><PERSON> hệ không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.id-he-giay-to.place-holder": "<PERSON><PERSON><PERSON>  ", "function.title.modal.form.id-phong-giay-to": "<PERSON><PERSON><PERSON> ph<PERSON>ng ", "function.title.modal.form.id-phong-giay-to.required": "Tên phòng không được để trống", "function.title.modal.form.id-phong-giay-to.place-holder": "<PERSON><PERSON><PERSON> ph<PERSON>ng ", "function.type-of-training.modal.form.tuyen-sinh": "<PERSON><PERSON><PERSON><PERSON>h", "function.title.modal.title-add.loai-giay-to": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON>i gi<PERSON>y tờ", "function.title.modal.title-info.loai-giay-to": "<PERSON>em chi tiết lo<PERSON>i gi<PERSON>y tờ", "function.title.modal.title-edit.loai-giay-to": "<PERSON><PERSON><PERSON> nh<PERSON>t lo<PERSON>i g<PERSON>y tờ", "function.title.confirm-delete.loai-giay-to": "Bạn có chắc chắn muốn xóa loại giấy tờ này", "function.title.page.title-loai-giay-to": "<PERSON><PERSON> s<PERSON>ch lo<PERSON>i gi<PERSON>y tờ", "menu.title-hoc-ky-dang-ky": "<PERSON><PERSON><PERSON> kỳ đăng ký", "function.title.table.name.ky-dang-ky": "<PERSON><PERSON> đăng ký", "function.title.table.name.dot": "<PERSON><PERSON><PERSON>", "function.title.table.name.hoc-ky": "<PERSON><PERSON><PERSON>", "function.title.table.name.nam-hoc": "<PERSON><PERSON><PERSON>", "function.title.table.name.tu-ngay": "<PERSON><PERSON> ngày", "function.title.table.name.den-ngay": "<PERSON><PERSON><PERSON>", "function.title.table.name.chon-dang-ky": "<PERSON><PERSON><PERSON> đ<PERSON>ng ký", "function.title.table.name.khoa-tkb": "Khóa tkb", "function.title.confirm-delete.hoc-ky-dang-ky": "Bạn có chắc chắn muốn xóa kỳ đăng ký này", "tu-ngay.modal.form.tu-ngay-hoc-ky-dang-ky.required": "Từ ngày không đ<PERSON><PERSON><PERSON> để trống", "tu-ngay.modal.form.tu-ngay-hoc-ky-dang-ky": "<PERSON><PERSON> ngày", "den-ngay.modal.form.den-ngay-hoc-ky-dang-ky.required": "Từ ngày không đ<PERSON><PERSON><PERSON> để trống", "den-ngay.modal.form.den-ngay-hoc-ky-dang-ky": "<PERSON><PERSON> ngày", "function.type-of-training.modal.form.chon-dang-ky": "<PERSON><PERSON><PERSON> đ<PERSON>ng ký", "function.type-of-training.modal.form.khoa-tkb": "Khóa tkb", "function.title.modal.title-add.hoc-ky-dang-ky": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> kỳ đăng ký", "function.title.modal.title-info.hoc-ky-dang-ky": "<PERSON>em chi ti<PERSON><PERSON> học kỳ đăng ký", "function.title.modal.title-edit.hoc-ky-dang-ky": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> học kỳ đăng ký", "function.title.modal.form.dot-hoc-ky-dang-ky": "<PERSON><PERSON><PERSON>", "function.title.modal.form.dot-hoc-ky-dang-ky.required": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.dot-hoc-ky-dang-ky.place-holder": "<PERSON><PERSON><PERSON>", "function.title.modal.form.hoc-ky-dang-ky": "<PERSON><PERSON><PERSON>", "function.title.modal.form.hoc-ky-dang-ky.required": "<PERSON><PERSON><PERSON> kỳ không đư<PERSON><PERSON> để trống", "function.title.modal.form.hoc-ky-dang-ky.place-holder": "<PERSON><PERSON><PERSON>", "function.title.modal.form.nam-hoc-dang-ky": "<PERSON><PERSON><PERSON>", "function.title.modal.form.nam-hoc-dang-ky.required": "<PERSON><PERSON><PERSON> h<PERSON> không đư<PERSON><PERSON> để trống", "function.title.modal.form.nam-hoc-dang-ky.place-holder": "<PERSON><PERSON><PERSON>", "function.title.modal.form.tu-ngay-dang-ky": "<PERSON><PERSON> ngày", "function.title.modal.form.tu-ngay-dang-ky.required": "Từ ngày không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.den-ngay-dang-ky": "<PERSON><PERSON><PERSON>", "function.title.modal.form.den-ngay-dang-ky.required": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "function.title.page.title-hoc-ky-dang-ky": "<PERSON><PERSON> s<PERSON>ch học kỳ đăng ký", "menu.title-phuong-ngoai-tru": "Phường ngoại trú", "function.title.page.title-phuong-ngoai-tru": "<PERSON><PERSON> s<PERSON>ch phường ngoại trú", "function.title.table.name.id-phuong": "Id <PERSON><PERSON><PERSON><PERSON>", "function.title.table.name.ten-phuong": "<PERSON><PERSON><PERSON>", "function.title.confirm-delete.phuong-ngoai-tru": "Bạn có chắc chắn muốn xóa phường ngoại trú này", "function.title.modal.form.id-phuong-ngoai-tru": "Id <PERSON><PERSON><PERSON><PERSON>", "function.title.modal.form.id-phuong-ngoai-tru.required": "Id ph<PERSON><PERSON><PERSON> không đư<PERSON><PERSON> để trống", "function.title.modal.form.id-phuong-ngoai-tru.place-holder": "Id ph<PERSON><PERSON>ng ngoại trú", "function.title.modal.form.ten-phuong-ngoai-tru": "<PERSON><PERSON><PERSON>", "function.title.modal.form.ten-phuong-ngoai-tru.required": "Tên phườ<PERSON> không được để trống", "function.title.modal.form.ten-phuong-ngoai-tru.place-holder": "Tên phư<PERSON>ng ngoại trú", "function.title.modal.title-add.phuong-ngoai-tru": "Thêm phường ngoại trú", "function.title.modal.title-info.phuong-ngoai-tru": "<PERSON>em chi tiết phường ngoại trú", "function.title.modal.title-edit.phuong-ngoai-tru": "<PERSON><PERSON><PERSON> nhật phường ngoại trú", "menu.title-hinh-thuc-hoc": "<PERSON><PERSON><PERSON> thức h<PERSON>c", "function.title.table.name.id-hinh-thuc-hoc": "<PERSON>d h<PERSON>nh thức học", "function.title.table.name.ma-hinh-thuc-hoc": "<PERSON><PERSON> hình thức học", "function.title.table.name.ten-hinh-thuc-hoc": "<PERSON><PERSON><PERSON> hình thức học", "function.title.table.name.ghi-chu-hinh-thuc-hoc": "<PERSON><PERSON> chú hình thức học", "function.title.page.title-hinh-thuc-hoc": "<PERSON><PERSON> s<PERSON>ch hình thức học", "function.title.confirm-delete.hinh-thuc-hoc": "Bạn có chắc chắn muốn xóa hình thức học này", "function.title.modal.form.id-hinh-thuc-hoc": "<PERSON>d h<PERSON>nh thức học", "function.title.modal.form.id-hinh-thuc-hoc.required": "Id h<PERSON><PERSON> thức học không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.id-hinh-thuc-hoc.place-holder": "<PERSON>d h<PERSON>nh thức học", "function.title.modal.form.ma-hinh-thuc-hoc": "<PERSON><PERSON> hình thức học", "function.title.modal.form.ma-hinh-thuc-hoc.required": "<PERSON><PERSON> hình thức học không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.ma-hinh-thuc-hoc.place-holder": "<PERSON><PERSON> hình thức học", "function.title.modal.form.ten-hinh-thuc-hoc": "<PERSON><PERSON><PERSON> hình thức học", "function.title.modal.form.ten-hinh-thuc-hoc.required": "<PERSON><PERSON><PERSON> hình thức học không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.ten-hinh-thuc-hoc.place-holder": "<PERSON><PERSON><PERSON> hình thức học", "function.title.modal.form.ghi-chu-hinh-thuc-hoc": "<PERSON><PERSON> chú hình thức học", "function.title.modal.form.ghi-chu-hinh-thuc-hoc.place-holder": "<PERSON><PERSON> chú hình thức học", "function.title.modal.title-add.hinh-thuc-hoc": "<PERSON><PERSON><PERSON><PERSON> hình thức học", "function.title.modal.title-info.hinh-thuc-hoc": "<PERSON>em chi ti<PERSON><PERSON> hình thức học", "function.title.modal.title-edit.hinh-thuc-hoc": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> hình thức học", "menu.title-xa": "Xã", "function.title.table.name.id-xa": "Mã xã", "function.title.table.name.id-huyen": "<PERSON><PERSON><PERSON>", "function.title.table.name.ten-xa": "<PERSON><PERSON><PERSON> xã", "function.title.table.name.ten-xa-en": "<PERSON><PERSON><PERSON> (English)", "function.title.page.title-xa": "<PERSON><PERSON> s<PERSON>ch xã", "function.title.confirm-delete.xa": "Bạn có chắc chắn muốn xóa xã này", "function.title.modal.form.id-xa": "Mã xã", "function.title.modal.form.id-xa.required": "<PERSON><PERSON> xã không đư<PERSON>c để trống", "function.title.modal.form.id-xa.place-holder": "Mã xã", "function.title.modal.form.id-huyen": "<PERSON><PERSON><PERSON>", "function.title.modal.form.id-huyen.required": "<PERSON><PERSON><PERSON> huy<PERSON>n không đư<PERSON><PERSON> để trống", "function.title.modal.form.id-huyen.place-holder": "<PERSON><PERSON><PERSON>", "function.title.modal.form.ten-xa": "<PERSON><PERSON><PERSON> xã", "function.title.modal.form.ten-xa.required": "<PERSON>ên xã không đư<PERSON>c để trống", "function.title.modal.form.ten-xa.place-holder": "<PERSON><PERSON><PERSON> xã", "function.title.modal.form.ten-xa-en": "<PERSON><PERSON><PERSON> (English)", "function.title.modal.form.ten-xa-en.place-holder": "<PERSON><PERSON><PERSON> (English)", "function.title.modal.title-add.xa": "<PERSON><PERSON><PERSON><PERSON> xã", "function.title.modal.title-info.xa": "<PERSON>em chi tiết xã", "function.title.modal.title-edit.xa": "<PERSON><PERSON><PERSON> nhật xã", "menu.title-xep-loai-hoc-bong": "<PERSON><PERSON><PERSON> lo<PERSON> h<PERSON> b<PERSON>ng", "function.title.table.name.id-xep-loai-hb": "<PERSON>d <PERSON><PERSON><PERSON> lo<PERSON> Hb", "function.title.table.name.ten-xep-loai": "<PERSON><PERSON><PERSON><PERSON>", "function.title.table.name.tu-diem-ht": "<PERSON>ừ điểm Ht", "function.title.table.name.tu-diem-rl": "<PERSON><PERSON> điểm Rl", "function.title.table.name.id-he": "<PERSON><PERSON><PERSON>", "function.title.table.name.tu-diem-ht-4": "Từ điểm Ht4", "function.title.table.name.ma-xep-loai": "<PERSON><PERSON> x<PERSON> lo<PERSON>", "function.title.table.name.so-tien": "<PERSON><PERSON> tiền", "function.title.page.title-xep-loai-hoc-bong": "<PERSON><PERSON><PERSON> lo<PERSON> h<PERSON> b<PERSON>ng", "function.title.confirm-delete.xep-loai-hoc-bong": "Bạn có chắc chắn muốn xóa xếp lo<PERSON>i học bổng này", "function.title.modal.form.id-xep-loai-hb": "<PERSON>d <PERSON><PERSON><PERSON> lo<PERSON> Hb", "function.title.modal.form.id-xep-loai-hb.required": "Id <PERSON><PERSON><PERSON> lo<PERSON>i Hb không đư<PERSON>c để trống", "function.title.modal.form.id-xep-loai-hb.place-holder": "<PERSON>d <PERSON><PERSON><PERSON> lo<PERSON> Hb", "function.title.modal.form.ten-xep-loai-hb": "<PERSON><PERSON><PERSON><PERSON>", "function.title.modal.form.ten-xep-loai-hb.required": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> lo<PERSON>i không đư<PERSON>c để trống", "function.title.modal.form.ten-xep-loai-hb.place-holder": "<PERSON><PERSON><PERSON><PERSON>", "function.title.modal.form.tu-diem-ht-hb": "<PERSON>ừ điểm Ht", "function.title.modal.form.tu-diem-ht-hb.required": "Từ điểm Ht không đư<PERSON><PERSON> để trống", "function.title.modal.form.tu-diem-ht-hb.place-holder": "<PERSON>ừ điểm Ht", "function.title.modal.form.tu-diem-rl-hb": "<PERSON><PERSON> điểm Rl", "function.title.modal.form.tu-diem-rl-hb.required": "Từ điểm R<PERSON> không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.tu-diem-rl-hb.place-holder": "<PERSON><PERSON> điểm Rl", "function.title.modal.form.id-he-hb": "<PERSON><PERSON><PERSON>", "function.title.modal.form.id-he-hb.required": "<PERSON><PERSON><PERSON> hệ không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.id-he-hb.place-holder": "<PERSON><PERSON><PERSON>", "function.title.modal.form.tu-diem-ht-4-hb": "Từ điểm Ht4", "function.title.modal.form.tu-diem-ht-4-hb.required": "Từ điểm Ht4 không được để trống", "function.title.modal.form.tu-diem-ht-4-hb.place-holder": "Từ điểm Ht4", "function.title.modal.form.ma-xep-loai-hb": "<PERSON><PERSON> x<PERSON> lo<PERSON>", "function.title.modal.form.ma-xep-loai-hb.required": "<PERSON><PERSON> xếp lo<PERSON>i không đư<PERSON><PERSON> để trống", "function.title.modal.form.ma-xep-loai-hb.place-holder": "<PERSON><PERSON> x<PERSON> lo<PERSON>", "function.title.modal.form.so-tien-hb": "<PERSON><PERSON> tiền", "function.title.modal.form.so-tien-hb.required": "<PERSON><PERSON> tiền không được để trống", "function.title.modal.form.so-tien-hb.place-holder": "<PERSON><PERSON> tiền", "function.title.modal.title-add.xep-loai-hoc-bong": "<PERSON><PERSON><PERSON><PERSON> x<PERSON><PERSON> lo<PERSON> h<PERSON> b<PERSON>ng", "function.title.modal.title-info.xep-loai-hoc-bong": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON> h<PERSON> b<PERSON>ng", "function.title.modal.title-edit.xep-loai-hoc-bong": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> lo<PERSON> h<PERSON> bổng", "menu.title-toa-nha": "Tòa nhà", "function.title.table.name.id-nha": "Id nh<PERSON>", "function.title.table.name.ma-nha": "Mã nhà", "function.title.table.name.ten-nha": "<PERSON><PERSON><PERSON>", "function.title.table.name.id-co-so": "<PERSON><PERSON><PERSON> c<PERSON> sở", "function.title.confirm-delete.toa-nha": "Bạn có chắc chắn muốn xóa tòa nhà này", "function.title.page.title-toa-nha": "<PERSON><PERSON> s<PERSON>ch tòa nhà", "function.title.modal.form.id-nha": "Id nh<PERSON>", "function.title.modal.form.id-nha.required": "Id <PERSON><PERSON><PERSON> không đư<PERSON><PERSON> để trống", "function.title.modal.form.id-nha.place-holder": "Id nh<PERSON>", "function.title.modal.form.ma-nha": "Mã nhà", "function.title.modal.form.ma-nha.required": "<PERSON><PERSON> nhà không đư<PERSON>c để trống", "function.title.modal.form.ma-nha.place-holder": "Mã nhà", "function.title.modal.form.ten-nha": "<PERSON><PERSON><PERSON>", "function.title.modal.form.ten-nha.required": "<PERSON><PERSON><PERSON> nh<PERSON> không được để trống", "function.title.modal.form.ten-nha.place-holder": "<PERSON><PERSON><PERSON>", "function.title.modal.form.id-co-so": "<PERSON><PERSON><PERSON> c<PERSON> sở", "function.title.modal.form.id-co-so.required": "<PERSON><PERSON><PERSON> cơ sở không được để trống", "function.title.modal.form.id-co-so.place-holder": "<PERSON><PERSON><PERSON> c<PERSON> sở", "function.title.modal.title-add.toa-nha": "<PERSON>h<PERSON><PERSON> tòa nhà", "function.title.modal.title-info.toa-nha": "<PERSON>em chi tiết tòa nhà", "function.title.modal.title-edit.toa-nha": "<PERSON><PERSON><PERSON> nhật tòa nhà", "function.title.table.name.id-doi-tuong-hoc-phi": "Id đ<PERSON><PERSON> t<PERSON> h<PERSON>c phí", "function.title.table.name.doi-tuong-hoc-phi": "<PERSON><PERSON><PERSON> t<PERSON> h<PERSON>c phí", "menu.title-doi-tuong-hoc-phi": "<PERSON><PERSON><PERSON> t<PERSON> h<PERSON>c phí", "function.title.page.title-doi-tuong-hoc-phi": "<PERSON><PERSON> s<PERSON>ch đối tư<PERSON> học phí", "function.title.confirm-delete.doi-tuong-hoc-phi": "Bạn có chắc chắn muốn xóa đối tượ<PERSON> học phí này", "function.title.modal.form.id-doi-tuong-hoc-phi": "Id đ<PERSON><PERSON> t<PERSON> h<PERSON>c phí", "function.title.modal.form.id-doi-tuong-hoc-phi.required": "Id đối tượng học phí không đư<PERSON><PERSON> để trống", "function.title.modal.form.id-doi-tuong-hoc-phi.place-holder": "Id đ<PERSON><PERSON> t<PERSON> h<PERSON>c phí", "function.title.modal.form.doi-tuong-hoc-phi": "<PERSON><PERSON><PERSON> t<PERSON> h<PERSON>c phí", "function.title.modal.form.doi-tuong-hoc-phi.required": "<PERSON><PERSON><PERSON> tượ<PERSON> học phí không đư<PERSON><PERSON> để trống", "function.title.modal.form.doi-tuong-hoc-phi.place-holder": "<PERSON><PERSON><PERSON> t<PERSON> h<PERSON>c phí", "function.title.modal.title-add.doi-tuong-hoc-phi": "<PERSON>h<PERSON><PERSON> đ<PERSON>i tư<PERSON> học phí", "function.title.modal.title-info.doi-tuong-hoc-phi": "<PERSON>em chi tiết đối tượ<PERSON> học phí", "function.title.modal.title-edit.doi-tuong-hoc-phi": "<PERSON><PERSON><PERSON> nhật đ<PERSON>i tượ<PERSON> học phí", "menu.title-bo-mon": "<PERSON><PERSON> môn", "function.title.table.name.id-bo-mon": "<PERSON>d b<PERSON> môn", "function.title.table.name.ma-bo-mon": "Mã bộ môn", "function.title.table.name.bo-mon": "<PERSON><PERSON> môn", "function.title.table.name.so-nhom": "Số nhóm", "function.title.page.title-bo-mon": "<PERSON><PERSON> s<PERSON>ch bộ môn", "function.title.confirm-delete.bo-mon": "Bạn có chắc chắn muốn xóa bộ môn này", "function.title.modal.form.id-bo-mon": "<PERSON>d b<PERSON> môn", "function.title.modal.form.id-bo-mon.required": "Id bộ môn không đư<PERSON><PERSON> để trống", "function.title.modal.form.id-bo-mon.place-holder": "<PERSON>d b<PERSON> môn", "function.title.modal.form.ma-bo-mon": "Mã bộ môn", "function.title.modal.form.ma-bo-mon.required": "<PERSON><PERSON> bộ môn không đư<PERSON><PERSON> để trống", "function.title.modal.form.ma-bo-mon.place-holder": "Mã bộ môn", "function.title.modal.form.bo-mon": "<PERSON><PERSON> môn", "function.title.modal.form.bo-mon.required": "<PERSON><PERSON> môn không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.bo-mon.place-holder": "<PERSON><PERSON> môn", "function.title.modal.form.so-nhom": "Số nhóm", "function.title.modal.form.so-nhom.required": "S<PERSON> nhóm không được để trống", "function.title.modal.form.so-nhom.place-holder": "Số nhóm", "function.title.modal.title-add.bo-mon": "<PERSON><PERSON><PERSON><PERSON> bộ môn", "function.title.modal.title-info.bo-mon": "<PERSON><PERSON> chi ti<PERSON><PERSON> bộ môn", "function.title.modal.title-edit.bo-mon": "<PERSON><PERSON><PERSON> nh<PERSON>t bộ môn", "menu.title-co-so-dao-tao": "Cơ sở đào tạo", "function.title.table.name.ma-co-so": "Mã cơ sở", "function.title.table.name.ten-co-so": "<PERSON><PERSON><PERSON> c<PERSON> sở", "function.title.table.name.day-ngoai-truong": "<PERSON><PERSON><PERSON> ngoài trường", "function.title.table.name.gd-cong-viec": "Giảng d<PERSON><PERSON> công vi<PERSON>c", "function.title.page.title-co-so-dao-tao": "<PERSON><PERSON> s<PERSON>ch cơ sở đào tạo", "function.title.confirm-delete.co-so-dao-tao": "<PERSON><PERSON> chắc chắn muốn xóa cơ sở đào tạo này", "function.type-of-training.modal.form.day-ngoai-truong": "<PERSON><PERSON><PERSON> ngoài trường", "function.type-of-training.modal.form.gd-cong-viec": "Giảng d<PERSON><PERSON> công vi<PERSON>c", "function.title.modal.title-add.co-so-dao-tao": "<PERSON><PERSON><PERSON><PERSON> cơ sở đào tạo", "function.title.modal.title-info.co-so-dao-tao": "<PERSON>em chi tiết cơ sở đào tạo", "function.title.modal.title-edit.co-so-dao-tao": "<PERSON><PERSON><PERSON> nh<PERSON>t cơ sở đào tạo", "function.title.modal.form.ma-co-so": "Mã cơ sở", "function.title.modal.form.ma-co-so.required": "<PERSON><PERSON> cơ sở không được để trống", "function.title.modal.form.ma-co-so.place-holder": "Mã cơ sở", "function.title.modal.form.ten-co-so": "<PERSON><PERSON><PERSON> c<PERSON> sở", "function.title.modal.form.ten-co-so.required": "<PERSON><PERSON><PERSON> c<PERSON> sở không là bộ", "function.title.modal.form.ten-co-so.place-holder": "<PERSON><PERSON><PERSON> c<PERSON> sở", "menu.title-diem-ren-luyen-quy-doi": "<PERSON><PERSON><PERSON><PERSON> rèn luyện quy đổi", "function.title.table.name.id-xep-loai": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "function.title.table.name.xep-loai": "<PERSON><PERSON><PERSON>", "function.title.table.name.tu-diem": "<PERSON><PERSON> điểm", "function.title.table.name.den-diem": "<PERSON><PERSON><PERSON> đi<PERSON>", "function.title.table.name.diem-cong-10": "<PERSON><PERSON><PERSON><PERSON> cộng 10", "function.title.table.name.diem-cong-4": "<PERSON><PERSON><PERSON><PERSON> cộ<PERSON> 4", "function.title.page.title-diem-ren-luyen-quy-doi": "<PERSON><PERSON> sách điểm rèn luyện quy đổi", "function.title.modal.form.id-xep-loai": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "function.title.modal.form.id-xep-loai.required": "Id x<PERSON><PERSON> lo<PERSON>i không đư<PERSON><PERSON> để trống", "function.title.modal.form.id-xep-loai.place-holder": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "function.title.modal.form.xep-loai": "<PERSON><PERSON><PERSON>", "function.title.modal.form.xep-loai.required": "<PERSON><PERSON><PERSON> lo<PERSON> không đư<PERSON><PERSON> để trống", "function.title.modal.form.xep-loai.place-holder": "<PERSON><PERSON><PERSON>", "function.title.modal.form.tu-diem": "<PERSON><PERSON> điểm", "function.title.modal.form.tu-diem.required": "Từ điểm không đư<PERSON><PERSON> để trống", "function.title.modal.form.tu-diem.place-holder": "<PERSON><PERSON> điểm", "function.title.modal.form.den-diem": "<PERSON><PERSON><PERSON> đi<PERSON>", "function.title.modal.form.den-diem.required": "<PERSON><PERSON><PERSON> điểm không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.den-diem.place-holder": "<PERSON><PERSON><PERSON> đi<PERSON>", "function.title.modal.form.diem-cong-10": "<PERSON><PERSON><PERSON><PERSON> cộng 10", "function.title.modal.form.diem-cong-10.required": "<PERSON><PERSON><PERSON><PERSON> cộng 10 không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.diem-cong-10.place-holder": "<PERSON><PERSON><PERSON><PERSON> cộng 10", "function.title.modal.form.diem-cong-4": "<PERSON><PERSON><PERSON><PERSON> cộ<PERSON> 4", "function.title.modal.form.diem-cong-4.required": "<PERSON><PERSON><PERSON><PERSON> cộng 4 không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.diem-cong-4.place-holder": "<PERSON><PERSON><PERSON><PERSON> cộ<PERSON> 4", "function.title.modal.title-add.diem-ren-luyen-quy-doi": "Thêm điểm rèn luyện quy đổi", "function.title.modal.title-info.diem-ren-luyen-quy-doi": "<PERSON>em chi tiết điểm rèn luyện quy đổi", "function.title.modal.title-edit.diem-ren-luyen-quy-doi": "<PERSON><PERSON><PERSON> nhật điểm rèn luyện quy đổi", "function.title.table.name.id-moi-thuc-tap": "<PERSON>d n<PERSON><PERSON> thực tập", "function.title.table.name.ma-noi-thuc-tap": "<PERSON><PERSON> n<PERSON>i thực tập", "function.title.table.name.noi-thuc-tap": "<PERSON><PERSON><PERSON> thực tập", "function.title.table.name.dia-chi-thuc-tap": "<PERSON><PERSON><PERSON> chỉ thực tập", "function.title.page.title-don-vi-thuc-tap": "<PERSON><PERSON> s<PERSON>ch đơn vị thực tập", "menu.title-don-vi-thuc-tap": "Đơn vị thực tập", "function.title.confirm-delete.don-vi-thuc-tap": "Bạn có chắc chắn muốn xóa đơn vị thực tập này", "function.title.modal.form.id-noi-thuc-tap": "<PERSON>d n<PERSON><PERSON> thực tập", "function.title.modal.form.id-noi-thuc-tap.required": "Id <PERSON><PERSON><PERSON> thực tập không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.id-noi-thuc-tap.place-holder": "<PERSON>d n<PERSON><PERSON> thực tập", "function.title.modal.form.ma-noi-thuc-tap": "<PERSON><PERSON> n<PERSON>i thực tập", "function.title.modal.form.ma-noi-thuc-tap.required": "<PERSON><PERSON> n<PERSON>i thực tập không đư<PERSON><PERSON> để trống", "function.title.modal.form.ma-noi-thuc-tap.place-holder": "<PERSON><PERSON> n<PERSON>i thực tập", "function.title.modal.form.noi-thuc-tap": "<PERSON><PERSON><PERSON> thực tập", "function.title.modal.form.noi-thuc-tap.required": "<PERSON><PERSON><PERSON> thực tập không đư<PERSON>c để trống", "function.title.modal.form.noi-thuc-tap.place-holder": "<PERSON><PERSON><PERSON> thực tập", "function.title.modal.form.dia-chi-thuc-tap": "<PERSON><PERSON><PERSON> chỉ thực tập", "function.title.modal.form.dia-chi-thuc-tap.place-holder": "<PERSON><PERSON><PERSON> chỉ thực tập", "function.title.modal.title-add.don-vi-thuc-tap": "<PERSON><PERSON><PERSON><PERSON> đơn vị thực tập", "function.title.modal.title-info.don-vi-thuc-tap": "<PERSON>em chi tiết đơn vị thực tập", "function.title.modal.title-edit.don-vi-thuc-tap": "<PERSON><PERSON><PERSON> nh<PERSON>t đơn vị thực tập", "menu.title-loai-quyet-dinh": "<PERSON><PERSON><PERSON> quyết đ<PERSON>nh", "function.title.page.title-loai-quyet-dinh": "<PERSON><PERSON> s<PERSON>ch loại quyết đ<PERSON>nh", "function.title.confirm-delete.loai-quyet-dinh": "<PERSON><PERSON> chắc chắn muốn xóa loại quyết định này", "function.title.table.name.id-loai-quyet-dinh": "<PERSON>d <PERSON><PERSON><PERSON> quyết đ<PERSON>nh", "function.title.table.name.ma-loai-quyet-dinh": "<PERSON><PERSON> loại quyết định", "function.title.table.name.ten-loai-quyet-dinh": "<PERSON><PERSON><PERSON> lo<PERSON><PERSON> quyết đ<PERSON>nh", "function.title.table.name.chuyen-lop": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>p", "function.title.table.name.thoi-hoc": "<PERSON><PERSON><PERSON><PERSON>", "function.title.table.name.ngung-hoc": "<PERSON><PERSON><PERSON> h<PERSON>", "function.title.table.name.hoc-tiep": "<PERSON><PERSON><PERSON>", "function.title.table.name.chuyen-truong-di": "Chuyển trư<PERSON><PERSON> đi", "function.title.table.name.chuyen-truong-den": "<PERSON><PERSON><PERSON>n trư<PERSON><PERSON> đến", "function.title.table.name.thoi-hoc-quy-che": "<PERSON><PERSON><PERSON><PERSON> học quy chế", "function.title.table.name.xoa-ten-khoi-lop": "<PERSON><PERSON><PERSON> tên khỏi lớp", "function.title.modal.form.id-loai-quyet-dinh": "<PERSON>d <PERSON><PERSON><PERSON> quyết đ<PERSON>nh", "function.title.modal.form.id-loai-quyet-dinh.required": "Id lo<PERSON><PERSON> quyết định không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.id-loai-quyet-dinh.place-holder": "<PERSON>d <PERSON><PERSON><PERSON> quyết đ<PERSON>nh", "function.title.modal.form.ma-loai-quyet-dinh": "<PERSON><PERSON> quyết định", "function.title.modal.form.ma-loai-quyet-dinh.required": "<PERSON><PERSON> quyết định không được để trống", "function.title.modal.form.ma-loai-quyet-dinh.place-holder": "<PERSON><PERSON> quyết định", "function.title.modal.form.ten-loai-quyet-dinh": "<PERSON><PERSON><PERSON> lo<PERSON><PERSON> quyết đ<PERSON>nh", "function.title.modal.form.ten-loai-quyet-dinh.required": "<PERSON><PERSON><PERSON> lo<PERSON><PERSON> quyết định không đư<PERSON><PERSON> để trống", "function.title.modal.form.ten-loai-quyet-dinh.place-holder": "<PERSON><PERSON><PERSON> lo<PERSON><PERSON> quyết đ<PERSON>nh", "function.type-of-training.modal.form.chuyen-lop": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>p", "function.type-of-training.modal.form.thoi-hoc": "<PERSON><PERSON><PERSON><PERSON>", "function.type-of-training.modal.form.ngung-hoc": "<PERSON><PERSON><PERSON> h<PERSON>", "function.type-of-training.modal.form.hoc-tiep": "<PERSON><PERSON><PERSON>", "function.type-of-training.modal.form.chuyen-truong-di": "<PERSON><PERSON><PERSON><PERSON> trư<PERSON><PERSON> đi", "function.type-of-training.modal.form.chuyen-truong-den": "<PERSON><PERSON><PERSON><PERSON> trư<PERSON><PERSON> đến", "function.type-of-training.modal.form.thoi-hoc-quy-che": "<PERSON><PERSON><PERSON><PERSON> học quy chế", "function.type-of-training.modal.form.xoa-ten-khoi-lop": "<PERSON><PERSON><PERSON> tên kh<PERSON>i lớp", "function.title.modal.title-add.loai-quyet-dinh": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON> quyết đ<PERSON>nh", "function.title.modal.title-info.loai-quyet-dinh": "<PERSON>em chi tiết loại quyết định", "function.title.modal.title-edit.loai-quyet-dinh": "<PERSON><PERSON><PERSON> nh<PERSON>t lo<PERSON>i quyết định", "menu.title-hinh-thuc-thi": "<PERSON><PERSON><PERSON> thức thi", "function.title.page.title-hinh-thuc-thi": "<PERSON><PERSON> s<PERSON>ch hình thức thi", "function.title.table.name.id-hinh-thuc-thi": "Id hình thức thi", "function.title.table.name.ma-hinh-thuc-thi": "<PERSON><PERSON> hình thức thi", "function.title.table.name.hinh-thuc-thi": "<PERSON><PERSON><PERSON> thức thi", "function.title.table.name.ghi-chu-hinh-thuc-thi": "<PERSON><PERSON><PERSON>", "function.title.table.name.khong-kiem-tra-trung-lich": "<PERSON><PERSON><PERSON><PERSON> kiểm tra trùng lịch", "function.title.confirm-delete.hinh-thuc-thi": "Bạn có chắc chắn muốn xóa hình thức thi này", "function.title.modal.form.id-hinh-thuc-thi": "Id hình thức thi", "function.title.modal.form.id-hinh-thuc-thi.required": "Id h<PERSON>nh thức thi không đư<PERSON>c để trống", "function.title.modal.form.id-hinh-thuc-thi.place-holder": "Id hình thức thi", "function.title.modal.form.ma-hinh-thuc-thi": "<PERSON><PERSON> hình thức thi", "function.title.modal.form.ma-hinh-thuc-thi.required": "<PERSON><PERSON> hình thức thi không đư<PERSON>c để trống", "function.title.modal.form.ma-hinh-thuc-thi.place-holder": "<PERSON><PERSON> hình thức thi", "function.title.modal.form.hinh-thuc-thi": "<PERSON><PERSON><PERSON> thức thi", "function.title.modal.form.hinh-thuc-thi.required": "<PERSON><PERSON><PERSON> thức thi không đ<PERSON><PERSON><PERSON> để trống", "function.title.modal.form.hinh-thuc-thi.place-holder": "<PERSON><PERSON><PERSON> thức thi", "function.title.modal.form.ghi-chu-hinh-thuc-thi": "<PERSON><PERSON><PERSON>", "function.title.modal.form.ghi-chu-hinh-thuc-thi.place-holder": "<PERSON><PERSON> chú hình thức thi", "function.type-of-training.modal.form.khong-kiem-tra-trung-lich": "<PERSON><PERSON><PERSON><PERSON> kiểm tra trùng lịch", "function.title.modal.title-add.hinh-thuc-thi": "<PERSON><PERSON><PERSON><PERSON> hình thức thi", "function.title.modal.title-info.hinh-thuc-thi": "<PERSON>em chi tiết hình thức thi", "function.title.modal.title-edit.hinh-thuc-thi": "<PERSON><PERSON><PERSON> nh<PERSON>t hình thức thi", "menu.title-phong-ban": "Phòng ban", "function.training-system.table.ma-phong-ban": "Mã phòng", "function.training-system.table.id-phong-ban": "Id ph<PERSON>ng", "function.training-system.table.phong-ban": "Phòng", "function.training-system.page.title-phong-ban": "<PERSON><PERSON> s<PERSON>ch phòng ban", "function.training-system.confirm-delete.title-phong-ban": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.training-system.confirm-delete.content-phong-ban": "Bạn có chắc chắn muốn xóa phòng ban này", "function.training-system.modal.form.ma-phong-ban.required": "<PERSON><PERSON> phòng không được để trống", "function.training-system.modal.form.ma-phong-ban.place-holder": "Mã phòng", "function.training-system.modal.form.ma-phong-ban": "Mã phòng", "function.training-system.modal.form.phong-ban.required": "<PERSON><PERSON><PERSON> không đư<PERSON><PERSON> để trống", "function.training-system.modal.form.phong-ban.place-holder": "Phòng", "function.training-system.modal.form.phong-ban": "Phòng", "function.training-system.modal.form.id-phong-ban.required": "Id phòng không được để trống", "function.training-system.modal.form.id-phong-ban.place-holder": "Id ph<PERSON>ng", "function.training-system.modal.form.id-phong-ban": "Id ph<PERSON>ng", "function.training-system.modal.title-add-phong-ban": "<PERSON>h<PERSON><PERSON> mới phòng ban", "function.training-system.modal.title-info-phong-ban": "<PERSON>em chi tiết phòng ban", "function.training-system.modal.title-edit-phong-ban": "<PERSON><PERSON><PERSON> phòng ban", "menu.title-khoi-kien-thuc": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>n thức", "function.khoi-kien-thuc.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.khoi-kien-thuc.table.mon-chuyen-nganh": "<PERSON><PERSON><PERSON> chuyên ng<PERSON>nh", "function.khoi-kien-thuc.table.name": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>n thức", "function.khoi-kien-thuc.table.name-en": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>(English)", "function.khoi-kien-thuc.modal.form.mon-chuyen-nganh": "<PERSON><PERSON><PERSON> chuyên ng<PERSON>nh ", "function.khoi-kien-thuc.modal.form.mon-chuyen-nganh.required": "<PERSON><PERSON><PERSON> chuyên ngành không được để trống", "function.khoi-kien-thuc.modal.form.mon-chuyen-nganh.place-holder": "<PERSON><PERSON><PERSON> chuyên ng<PERSON>nh ", "function.khoi-kien-thuc.modal.form.name": "<PERSON><PERSON><PERSON> kh<PERSON>i kiến thức ", "function.khoi-kien-thuc.modal.form.name.required": "<PERSON>ê<PERSON> kh<PERSON>i kiến thức không đư<PERSON><PERSON> để trống", "function.khoi-kien-thuc.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> kh<PERSON>i kiến thức ", "function.khoi-kien-thuc.modal.form.name-En": "<PERSON><PERSON><PERSON> kh<PERSON>i kiế<PERSON>(English)", "function.khoi-kien-thuc.modal.form.name-En.required": "Tên khối kiến thức  bằng tiếng anh không được để trống", "function.khoi-kien-thuc.modal.form.name-En.place-holder": "T<PERSON>n kh<PERSON>i kiến thức  bằng tiếng anh", "function.khoi-kien-thuc.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.khoi-kien-thuc.confirm-delete.content": "Bạn có chắc chắn muốn xóa khối kiến thức này", "function.khoi-kien-thuc.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>i kiến thức", "function.khoi-kien-thuc.modal.title-info": "<PERSON>em chi tiết kh<PERSON>i kiến thức", "function.khoi-kien-thuc.modal.title-edit": "<PERSON><PERSON><PERSON> kh<PERSON><PERSON> kiến thức", "function.khoi-kien-thuc.page.title": "<PERSON><PERSON> s<PERSON>ch kh<PERSON>i kiến thức", "menu.title-diem-quy-doi-thang-4": "<PERSON><PERSON><PERSON><PERSON> quy đổi thang 4", "function.diem-quy-doi.table.tu-nam-hoc": "<PERSON><PERSON> năm học", "function.diem-quy-doi.table.den-nam-hoc": "<PERSON><PERSON><PERSON> n<PERSON> h<PERSON>c", "function.diem-quy-doi.table.tu-hoc-ky": "<PERSON><PERSON> học kỳ", "function.diem-quy-doi.table.den-hoc-ky": "<PERSON><PERSON><PERSON>", "function.diem-quy-doi.table.xep-loai": "<PERSON><PERSON><PERSON>", "function.diem-quy-doi.table.diem-chu": "<PERSON><PERSON><PERSON><PERSON> chữ", "function.diem-quy-doi.table.diem-so": "<PERSON><PERSON><PERSON><PERSON>", "function.diem-quy-doi.table.tu-diem": "<PERSON><PERSON> điểm", "function.diem-quy-doi.table.den-diem": "<PERSON><PERSON><PERSON> đi<PERSON>", "function.diem-quy-doi.table.tich-luy": "<PERSON><PERSON><PERSON>", "function.diem-quy-doi.table.ten-he": "<PERSON><PERSON><PERSON>", "function.diem-quy-doi.modal.form.tu-nam-hoc": "<PERSON><PERSON> năm học ", "function.diem-quy-doi.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.diem-quy-doi.modal.form.tu-nam-hoc.required": "Từ năm học không đ<PERSON><PERSON><PERSON> để trống", "function.diem-quy-doi.modal.form.tu-nam-hoc.place-holder": "<PERSON><PERSON> năm học ", "function.diem-quy-doi.modal.form.tu-hoc-ky": "<PERSON><PERSON> học kỳ ", "function.diem-quy-doi.modal.form.natu-hoc-kyme.required": "<PERSON><PERSON> học kỳ không đ<PERSON><PERSON><PERSON> để trống", "function.diem-quy-doi.modal.form.tu-hoc-ky.place-holder": "<PERSON><PERSON> học kỳ ", "function.diem-quy-doi.modal.form.den-nam-hoc": "<PERSON><PERSON><PERSON> n<PERSON> h<PERSON>c ", "function.diem-quy-doi.modal.form.den-nam-hoc.required": "<PERSON><PERSON><PERSON> năm học không đ<PERSON><PERSON><PERSON> để trống", "function.diem-quy-doi.modal.form.den-nam-hoc.place-holder": "<PERSON><PERSON><PERSON> n<PERSON> h<PERSON>c ", "function.diem-quy-doi.modal.form.den-hoc-ky": "<PERSON><PERSON><PERSON> ", "function.diem-quy-doi.modal.form.den-hoc-ky.required": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> kỳ không đ<PERSON><PERSON><PERSON> để trống", "function.diem-quy-doi.modal.form.den-hoc-ky.place-holder": "<PERSON><PERSON><PERSON> ", "function.diem-quy-doi.modal.form.xep-loai": "<PERSON><PERSON><PERSON> ", "function.diem-quy-doi.modal.form.xep-loai.required": "<PERSON><PERSON><PERSON> lo<PERSON> không đư<PERSON><PERSON> để trống", "function.diem-quy-doi.modal.form.xep-loai.place-holder": "<PERSON><PERSON><PERSON> ", "function.diem-quy-doi.modal.form.diem-chu": "<PERSON><PERSON><PERSON><PERSON> chữ ", "function.diem-quy-doi.modal.form.diem-chu.required": "<PERSON><PERSON><PERSON><PERSON> chữ không đư<PERSON><PERSON> để trống", "function.diem-quy-doi.modal.form.diem-chu.place-holder": "<PERSON><PERSON><PERSON><PERSON> chữ ", "function.diem-quy-doi.modal.form.diem-so": "<PERSON><PERSON><PERSON><PERSON> ", "function.diem-quy-doi.modal.form.diem-so.required": "<PERSON><PERSON><PERSON><PERSON> số không đ<PERSON><PERSON><PERSON> để trống", "function.diem-quy-doi.modal.form.diem-so.place-holder": "<PERSON><PERSON><PERSON><PERSON> ", "function.diem-quy-doi.modal.form.tu-diem": "<PERSON><PERSON> điểm ", "function.diem-quy-doi.modal.form.tu-diem.required": "Từ điểm không đư<PERSON><PERSON> để trống", "function.diem-quy-doi.modal.form.tu-diem.place-holder": "<PERSON><PERSON> điểm ", "function.diem-quy-doi.modal.form.den-diem": "<PERSON><PERSON><PERSON> đi<PERSON> ", "function.diem-quy-doi.modal.form.den-diem.required": "<PERSON><PERSON><PERSON> điểm không đ<PERSON><PERSON><PERSON> để trống", "function.diem-quy-doi.modal.form.den-diem.place-holder": "<PERSON><PERSON><PERSON> đi<PERSON> ", "function.diem-quy-doi.modal.form.tich-luy": "<PERSON><PERSON><PERSON> ", "function.diem-quy-doi.modal.form.tich-luy.required": "<PERSON><PERSON><PERSON> l<PERSON> không đư<PERSON><PERSON> để trống", "function.diem-quy-doi.modal.form.tich-luy.place-holder": "<PERSON><PERSON><PERSON> ", "function.diem-quy-doi.modal.form.ten-he": "<PERSON><PERSON><PERSON> ", "function.diem-quy-doi.modal.form.ten-he.required": "<PERSON><PERSON><PERSON> hệ không đ<PERSON><PERSON><PERSON> để trống", "function.diem-quy-doi.modal.form.ten-he.place-holder": "<PERSON><PERSON><PERSON> ", "function.diem-quy-doi.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.diem-quy-doi.confirm-delete.content": "<PERSON>ạn có chắc chắn muốn xóa điểm quy đổi thang 4", "function.diem-quy-doi.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> điểm quy đổi thang 4", "function.diem-quy-doi.modal.title-info": "<PERSON><PERSON> chi tiết điểm quy đổi thang 4", "function.diem-quy-doi.modal.title-edit": "<PERSON><PERSON><PERSON> điểm quy đổi thang 4", "function.diem-quy-doi.page.title": "<PERSON><PERSON> s<PERSON>ch điểm quy đổi thang 4", "menu.title-loai-thanh-phan-diem-theo-he": "<PERSON><PERSON><PERSON><PERSON> phần điểm theo hệ", "function.thanh-phan-mon-theo-he.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.thanh-phan-mon-theo-he.table.stt": "STT", "function.thanh-phan-mon-theo-he.table.ty-le": "Tỷ lệ", "function.thanh-phan-mon-theo-he.table.ty-le-nhom": "Tỷ lệ nhóm", "function.thanh-phan-mon-theo-he.table.nhom-thanh-phan": "<PERSON><PERSON><PERSON><PERSON> thành phần", "function.thanh-phan-mon-theo-he.table.chon-mac-dinh": "<PERSON><PERSON><PERSON> mặc đinh", "function.thanh-phan-mon-theo-he.table.ten-he": "<PERSON><PERSON><PERSON>", "function.thanh-phan-mon-theo-he.table.ten-thanh-phan": "<PERSON><PERSON><PERSON> thành ph<PERSON>n", "function.thanh-phan-mon-theo-he.modal.form.stt": "STT ", "function.thanh-phan-mon-theo-he.modal.form.stt.required": "STT không đư<PERSON><PERSON> để trống", "function.thanh-phan-mon-theo-he.modal.form.stt.place-holder": "STT ", "function.thanh-phan-mon-theo-he.modal.form.ty-le-nhom": "Tỷ lệ nhóm ", "function.thanh-phan-mon-theo-he.modal.form.naty-le-nhomme.required": "Tỷ lệ nhóm không được để trống", "function.thanh-phan-mon-theo-he.modal.form.ty-le-nhom.place-holder": "Tỷ lệ nhóm ", "function.thanh-phan-mon-theo-he.modal.form.ty-le": "Tỷ lệ ", "function.thanh-phan-mon-theo-he.modal.form.ty-le.required": "Tỷ lệ không đư<PERSON><PERSON> để trống", "function.thanh-phan-mon-theo-he.modal.form.ty-le.place-holder": "Tỷ lệ ", "function.thanh-phan-mon-theo-he.modal.form.nhom-thanh-phan": "<PERSON><PERSON><PERSON><PERSON> thành phần ", "function.thanh-phan-mon-theo-he.modal.form.nhom-thanh-phan.required": "<PERSON><PERSON><PERSON><PERSON> thành phần không đư<PERSON><PERSON> để trống", "function.thanh-phan-mon-theo-he.modal.form.nhom-thanh-phan.place-holder": "<PERSON><PERSON><PERSON><PERSON> thành phần ", "function.thanh-phan-mon-theo-he.modal.form.chon-mac-dinh": "<PERSON><PERSON><PERSON> mặc đinh ", "function.thanh-phan-mon-theo-he.modal.form.chon-mac-dinh.required": "<PERSON>ọn mặc đinh không được để trống", "function.thanh-phan-mon-theo-he.modal.form.chon-mac-dinh.place-holder": "<PERSON><PERSON><PERSON> mặc đinh ", "function.thanh-phan-mon-theo-he.modal.form.ten-he": "<PERSON><PERSON><PERSON> ", "function.thanh-phan-mon-theo-he.modal.form.ten-he.required": "<PERSON><PERSON><PERSON> hệ không đ<PERSON><PERSON><PERSON> để trống", "function.thanh-phan-mon-theo-he.modal.form.ten-he.place-holder": "<PERSON><PERSON><PERSON> ", "function.thanh-phan-mon-theo-he.modal.form.thanh-phan-diem": "<PERSON><PERSON><PERSON> thành phần điểm ", "function.thanh-phan-mon-theo-he.modal.form.thanh-phan-diem.required": "<PERSON>ên thành phần điểm không đư<PERSON><PERSON> để trống", "function.thanh-phan-mon-theo-he.modal.form.thanh-phan-diem.place-holder": "<PERSON><PERSON><PERSON> thành phần điểm ", "function.thanh-phan-mon-theo-he.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.thanh-phan-mon-theo-he.confirm-delete.content": "Bạn có chắc chắn muốn xóa thành phần môn theo hệ này", "function.thanh-phan-mon-theo-he.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> thành phần môn theo hệ", "function.thanh-phan-mon-theo-he.modal.title-info": "<PERSON>em chi tiết thành phần môn theo hệ", "function.thanh-phan-mon-theo-he.modal.title-edit": "<PERSON><PERSON><PERSON> thành phần môn theo hệ", "function.thanh-phan-mon-theo-he.page.title": "<PERSON><PERSON> s<PERSON>ch thành phần môn theo hệ", "menu.title-loai-thu-chi": "<PERSON><PERSON>i thu chi", "function.loai-thu-chi.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.loai-thu-chi.table.ma-thu-chi": "<PERSON>ã thu chi", "function.loai-thu-chi.table.ten-thu-chi": "Tên thu chi", "function.loai-thu-chi.table.thu-chi": "<PERSON>hu chi", "function.loai-thu-chi.table.so-tien": "<PERSON><PERSON> tiền", "function.loai-thu-chi.table.hoc-lai": "<PERSON><PERSON><PERSON>", "function.loai-thu-chi.table.thi-lai": "<PERSON><PERSON> l<PERSON>i", "function.loai-thu-chi.table.hoc-phi": "<PERSON><PERSON><PERSON> phí", "function.loai-thu-chi.table.khoan-thu-ktx": "<PERSON><PERSON><PERSON><PERSON> thu ktx", "function.loai-thu-chi.table.kinh-phi-dt": "Kinh phí dt", "function.loai-thu-chi.table.khoan-thu-tien-phong": "<PERSON><PERSON><PERSON><PERSON> thu tiền phòng", "function.loai-thu-chi.table.khoan-tien-coc": "<PERSON><PERSON><PERSON><PERSON> tiền c<PERSON>c", "function.loai-thu-chi.table.bao-hiem": "<PERSON><PERSON><PERSON>", "function.loai-thu-chi.modal.form.ma-thu-chi": "<PERSON>ã thu chi ", "function.loai-thu-chi.modal.form.ma-thu-chi.required": "<PERSON>ã thu chi không đư<PERSON><PERSON> để trống", "function.loai-thu-chi.modal.form.ma-thu-chi.place-holder": "<PERSON>ã thu chi ", "function.loai-thu-chi.modal.form.ten-thu-chi": "Tên thu chi ", "function.loai-thu-chi.modal.form.ten-thu-chi.required": "Tên thu chi không đ<PERSON><PERSON><PERSON> để trống", "function.loai-thu-chi.modal.form.ten-thu-chi.place-holder": "Tên thu chi ", "function.loai-thu-chi.modal.form.so-tien": "<PERSON><PERSON> tiền ", "function.loai-thu-chi.modal.form.so-tien.required": "<PERSON><PERSON> tiền", "function.loai-thu-chi.modal.form.so-tien.place-holder": "<PERSON><PERSON> tiền", "function.loai-thu-chi.modal.form.thu-chi": "<PERSON>hu chi", "function.loai-thu-chi.modal.form.hoc-lai": "<PERSON><PERSON><PERSON>", "function.loai-thu-chi.modal.form.thi-lai": "<PERSON><PERSON> l<PERSON>i", "function.loai-thu-chi.modal.form.hoc-phi": "<PERSON><PERSON><PERSON> phí", "function.loai-thu-chi.modal.form.khoan-thu-ktx": "<PERSON><PERSON><PERSON><PERSON> thu ktx", "function.loai-thu-chi.modal.form.kinh-phi-dt": "Kinh phí dt", "function.loai-thu-chi.modal.form.khoan-thu-tien-phong": "<PERSON><PERSON><PERSON><PERSON> thu tiền phòng", "function.loai-thu-chi.modal.form.khoan-tien-coc": "<PERSON><PERSON><PERSON><PERSON> tiền c<PERSON>c", "function.loai-thu-chi.modal.form.bao-hiem": "<PERSON><PERSON><PERSON>", "function.loai-thu-chi.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.loai-thu-chi.confirm-delete.content": "Bạn có chắc chắn muốn xóa thành phần môn theo hệ này", "function.loai-thu-chi.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> thành phần môn theo hệ", "function.loai-thu-chi.modal.title-info": "<PERSON>em chi tiết thành phần môn theo hệ", "function.loai-thu-chi.modal.title-edit": "<PERSON><PERSON><PERSON> thành phần môn theo hệ", "function.loai-thu-chi.page.title": "<PERSON><PERSON> s<PERSON>ch thành phần môn theo hệ", "menu.title-phong-hoc": "<PERSON><PERSON><PERSON>", "function.phong-hoc.table.ten-co-so": "<PERSON><PERSON><PERSON> c<PERSON> sở", "function.phong-hoc.table.ten-nha": "<PERSON><PERSON><PERSON>", "function.phong-hoc.table.ten-tang": "<PERSON><PERSON><PERSON>", "function.phong-hoc.table.so-phong": "Số phòng", "function.phong-hoc.table.loai-phong": "Loại phòng", "function.phong-hoc.table.suc-chua": "<PERSON><PERSON><PERSON>", "function.phong-hoc.table.so-sv": "Số sv", "function.phong-hoc.table.thiet-bi": "<PERSON><PERSON><PERSON><PERSON> bị", "function.phong-hoc.table.thuc-hanh": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh", "function.phong-hoc.table.ten-loai-phong": "<PERSON><PERSON><PERSON> lo<PERSON>i phòng", "function.phong-hoc.table.khong-to-chuc-thi": "<PERSON><PERSON><PERSON><PERSON> tổ chức thi", "function.phong-hoc.table.ghi-chu": "<PERSON><PERSON><PERSON>", "function.phong-hoc.table.trung-phong": "<PERSON> ph<PERSON>ng", "function.phong-hoc.table.ten-khoa": "<PERSON><PERSON><PERSON> k<PERSON>a", "function.phong-hoc.table.suc-chua-thi": "<PERSON><PERSON><PERSON> ch<PERSON>a thi", "function.phong-hoc.table.am-thanh": "<PERSON><PERSON>", "function.phong-hoc.table.may-tinh": "<PERSON><PERSON><PERSON>", "function.phong-hoc.table.ti-vi": "Ti vi", "function.phong-hoc.table.may-chieu": "<PERSON><PERSON><PERSON>", "function.phong-hoc.modal.form.ten-co-so": "<PERSON><PERSON><PERSON> c<PERSON> sở ", "function.phong-hoc.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.phong-hoc.modal.form.ten-co-so.required": "<PERSON><PERSON><PERSON> cơ sở không được để trống", "function.phong-hoc.modal.form.ten-co-so.place-holder": "<PERSON><PERSON><PERSON> c<PERSON> sở ", "function.phong-hoc.modal.form.ten-nha": "<PERSON><PERSON><PERSON> ", "function.phong-hoc.modal.form.ten-nha.required": "<PERSON><PERSON><PERSON> nh<PERSON> không được để trống", "function.phong-hoc.modal.form.ten-nha.place-holder": "<PERSON><PERSON><PERSON> ", "function.phong-hoc.modal.form.ten-tang": "<PERSON><PERSON><PERSON> ", "function.phong-hoc.modal.form.ten-tang.required": "<PERSON><PERSON><PERSON> tầng không đư<PERSON><PERSON> để trống", "function.phong-hoc.modal.form.ten-tang.place-holder": "<PERSON><PERSON><PERSON> ", "function.phong-hoc.modal.form.so-phong": "Số phòng ", "function.phong-hoc.modal.form.so-phong.required": "Số phòng không được để trống", "function.phong-hoc.modal.form.so-phong.place-holder": "Số phòng ", "function.phong-hoc.modal.form.loai-phong": "Loại phòng ", "function.phong-hoc.modal.form.loai-phong.required": "Loạ<PERSON> phòng không được để trống", "function.phong-hoc.modal.form.loai-phong.place-holder": "Loại phòng ", "function.phong-hoc.modal.form.so-ban": "Số bàn ", "function.phong-hoc.modal.form.so-ban.required": "<PERSON><PERSON> bàn không đư<PERSON><PERSON> để trống", "function.phong-hoc.modal.form.so-ban.place-holder": "Số bàn ", "function.phong-hoc.modal.form.suc-chua": "<PERSON><PERSON><PERSON> ", "function.phong-hoc.modal.form.suc-chua.required": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "function.phong-hoc.modal.form.suc-chua.place-holder": "<PERSON><PERSON><PERSON> ", "function.phong-hoc.modal.form.so-sv": "<PERSON><PERSON> sinh viên ", "function.phong-hoc.modal.form.so-sv.required": "<PERSON><PERSON> sinh viên không đư<PERSON>c để trống", "function.phong-hoc.modal.form.so-sv.place-holder": "<PERSON><PERSON> sinh viên ", "function.phong-hoc.modal.form.so-sv-mot-ban": "Số sv một bàn ", "function.phong-hoc.modal.form.so-sv-mot-ban.required": "Số sv một bàn không được để trống", "function.phong-hoc.modal.form.so-sv-mot-ban.place-holder": "Số sv một bàn ", "function.phong-hoc.modal.form.thiet-bi": "<PERSON><PERSON><PERSON><PERSON> bị ", "function.phong-hoc.modal.form.thiet-bi.required": "<PERSON><PERSON><PERSON><PERSON> bị không đư<PERSON><PERSON> để trống", "function.phong-hoc.modal.form.thiet-bi.place-holder": "<PERSON><PERSON><PERSON><PERSON> bị ", "function.phong-hoc.modal.form.thuc-hanh": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh ", "function.phong-hoc.modal.form.thuc-hanh.required": "<PERSON><PERSON><PERSON><PERSON> hành không đư<PERSON><PERSON> để trống", "function.phong-hoc.modal.form.thuc-hanh.place-holder": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh ", "function.phong-hoc.modal.form.ten-loai-phong": "<PERSON><PERSON><PERSON> lo<PERSON>i phòng ", "function.phong-hoc.modal.form.ten-loai-phong.required": "Tên lo<PERSON>i phòng không được để trống", "function.phong-hoc.modal.form.ten-loai-phong.place-holder": "<PERSON><PERSON><PERSON> lo<PERSON>i phòng ", "function.phong-hoc.modal.form.khong-to-chuc-thi": "<PERSON><PERSON><PERSON><PERSON> tổ chức thi ", "function.phong-hoc.modal.form.khong-to-chuc-thi.required": "<PERSON>hông tổ chức thi không đư<PERSON>c để trống", "function.phong-hoc.modal.form.khong-to-chuc-thi.place-holder": "<PERSON><PERSON><PERSON><PERSON> tổ chức thi ", "function.phong-hoc.modal.form.ghi-chu": "<PERSON><PERSON><PERSON> ", "function.phong-hoc.modal.form.ghi-chu.required": "<PERSON><PERSON> chú không đư<PERSON><PERSON> để trống", "function.phong-hoc.modal.form.ghi-chu.place-holder": "<PERSON><PERSON><PERSON> ", "function.phong-hoc.modal.form.trung-phong": "<PERSON> ph<PERSON>ng ", "function.phong-hoc.modal.form.trung-phong.required": "<PERSON> phòng không đư<PERSON>c để trống", "function.phong-hoc.modal.form.trung-phong.place-holder": "<PERSON> ph<PERSON>ng ", "function.phong-hoc.modal.form.ten-khoa": "<PERSON><PERSON><PERSON> k<PERSON>a ", "function.phong-hoc.modal.form.ten-khoa.required": "<PERSON><PERSON><PERSON> khoa không đư<PERSON>c để trống", "function.phong-hoc.modal.form.ten-khoa.place-holder": "<PERSON><PERSON><PERSON> k<PERSON>a ", "function.phong-hoc.modal.form.suc-chua-thi": "<PERSON><PERSON><PERSON> ch<PERSON>a thi ", "function.phong-hoc.modal.form.suc-chua-thi.required": "<PERSON><PERSON><PERSON> chứa thi không đ<PERSON><PERSON><PERSON> để trống", "function.phong-hoc.modal.form.suc-chua-thi.place-holder": "<PERSON><PERSON><PERSON> ch<PERSON>a thi ", "function.phong-hoc.modal.form.am-thanh": "<PERSON><PERSON> ", "function.phong-hoc.modal.form.am-thanh.required": "<PERSON><PERSON> <PERSON>h không đư<PERSON><PERSON> để trống", "function.phong-hoc.modal.form.am-thanh.place-holder": "<PERSON><PERSON> ", "function.phong-hoc.modal.form.may-tinh": "<PERSON><PERSON><PERSON> ", "function.phong-hoc.modal.form.may-tinh.required": "<PERSON><PERSON><PERSON> t<PERSON> không đư<PERSON>c để trống", "function.phong-hoc.modal.form.may-tinh.place-holder": "<PERSON><PERSON><PERSON> ", "function.phong-hoc.modal.form.ti-vi": "Tivi ", "function.phong-hoc.modal.form.ti-vi.required": "Tiv<PERSON> kh<PERSON>ng đ<PERSON><PERSON><PERSON> để trống", "function.phong-hoc.modal.form.ti-vi.place-holder": "Tivi ", "function.phong-hoc.modal.form.may-chieu": "<PERSON><PERSON><PERSON> ", "function.phong-hoc.modal.form.may-chieu.required": "<PERSON><PERSON><PERSON> ch<PERSON> không đư<PERSON><PERSON> để trống", "function.phong-hoc.modal.form.may-chieu.place-holder": "<PERSON><PERSON><PERSON> ", "function.phong-hoc.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.phong-hoc.confirm-delete.content": "Bạn có chắc chắn muốn xóa phòng học", "function.phong-hoc.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> phòng học", "function.phong-hoc.modal.title-info": "<PERSON>em chi tiết phòng học", "function.phong-hoc.modal.title-edit": "<PERSON><PERSON><PERSON> ph<PERSON>ng học", "function.phong-hoc.page.title": "<PERSON><PERSON> s<PERSON>ch phòng học", "menu.title-cap-ren-luyen": "<PERSON><PERSON><PERSON>", "function.cap-ren-luyen.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.cap-ren-luyen.table.ky-hieu": "<PERSON><PERSON>", "function.cap-ren-luyen.table.name": "<PERSON><PERSON><PERSON> c<PERSON>", "function.cap-ren-luyen.table.diem": "<PERSON><PERSON><PERSON><PERSON>", "function.cap-ren-luyen.modal.form.ky-hieu": "<PERSON><PERSON> ", "function.cap-ren-luyen.modal.form.ky-hieu.required": "<PERSON><PERSON> hiệu không đư<PERSON><PERSON> để trống", "function.cap-ren-luyen.modal.form.ky-hieu.place-holder": "<PERSON><PERSON> ", "function.cap-ren-luyen.modal.form.name": "<PERSON><PERSON><PERSON> c<PERSON> ", "function.cap-ren-luyen.modal.form.name.required": "<PERSON><PERSON><PERSON> cấ<PERSON> không đư<PERSON><PERSON> để trống", "function.cap-ren-luyen.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> c<PERSON> ", "function.cap-ren-luyen.modal.form.diem": "<PERSON><PERSON><PERSON><PERSON>", "function.cap-ren-luyen.modal.form.diem.required": "<PERSON><PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "function.cap-ren-luyen.modal.form.diem.place-holder": "<PERSON><PERSON><PERSON><PERSON>", "function.cap-ren-luyen.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.cap-ren-luyen.confirm-delete.content": "Bạn có chắc chắn muốn xóa cấp này", "function.cap-ren-luyen.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>p", "function.cap-ren-luyen.modal.title-info": "<PERSON>em chi ti<PERSON><PERSON> cấp", "function.cap-ren-luyen.modal.title-edit": "<PERSON><PERSON><PERSON> c<PERSON>p", "function.cap-ren-luyen.page.title": "<PERSON><PERSON> s<PERSON>ch c<PERSON>p r<PERSON><PERSON>", "menu.title-tang": "<PERSON><PERSON><PERSON>", "function.tang.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.tang.table.code": "<PERSON><PERSON> tầng", "function.tang.table.name": "<PERSON><PERSON><PERSON>", "function.tang.modal.form.code": "<PERSON><PERSON> tầng ", "function.tang.modal.form.code.required": "<PERSON><PERSON> tầng không đư<PERSON><PERSON> để trống", "function.tang.modal.form.code.place-holder": "<PERSON><PERSON> tầng ", "function.tang.modal.form.name": "<PERSON><PERSON><PERSON> ", "function.tang.modal.form.name.required": "<PERSON><PERSON><PERSON> tầng không đư<PERSON><PERSON> để trống", "function.tang.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> ", "function.tang.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.tang.confirm-delete.content": "Bạn có chắc chắn muốn xóa tầng này", "function.tang.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "function.tang.modal.title-info": "<PERSON>em chi tiết tầng", "function.tang.modal.title-edit": "<PERSON><PERSON><PERSON> t<PERSON>", "function.tang.page.title": "<PERSON><PERSON> s<PERSON>ch tầng ", "menu.title-loai-phong": "Loại phòng", "function.loai-phong.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.loai-phong.table.code": "Mã loại phòng", "function.loai-phong.table.name": "Loại phòng", "function.loai-phong.table.thuc-hanh": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh", "function.loai-phong.modal.form.code": "Mã loại phòng ", "function.loai-phong.modal.form.code.required": "Mã loại phòng không được để trống", "function.loai-phong.modal.form.code.place-holder": "Mã loại phòng ", "function.loai-phong.modal.form.name": "<PERSON><PERSON><PERSON> lo<PERSON>i phòng ", "function.loai-phong.modal.form.name.required": "Tên lo<PERSON>i phòng không được để trống", "function.loai-phong.modal.form.name.place-holder": "<PERSON><PERSON><PERSON> lo<PERSON>i phòng ", "function.loai-phong.modal.form.thuc-hanh": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh", "function.loai-phong.modal.form.thuc-hanh.required": "<PERSON><PERSON><PERSON><PERSON> hành không đư<PERSON><PERSON> để trống", "function.loai-phong.modal.form.thuc-hanh.place-holder": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh", "function.loai-phong.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.loai-phong.confirm-delete.content": "Bạn có chắc chắn muốn xóa loại phòng này", "function.loai-phong.modal.title-add": "<PERSON>h<PERSON><PERSON> lo<PERSON>i phòng", "function.loai-phong.modal.title-info": "<PERSON>em chi tiết lo<PERSON>i phòng", "function.loai-phong.modal.title-edit": "<PERSON><PERSON><PERSON> lo<PERSON>i phòng", "function.loai-phong.page.title": "<PERSON><PERSON> s<PERSON>ch loại phòng", "update-user-info.full-name.placeholder": "<PERSON><PERSON><PERSON><PERSON> họ tên", "update-user-info.full-name.required": "<PERSON><PERSON> tên không đư<PERSON><PERSON> để trống", "update-user-info.email.invalid-format": "<PERSON><PERSON><PERSON> d<PERSON>ng email không hợp lệ", "update-user-info.phoneNumber.invalid-format": "<PERSON><PERSON><PERSON> dạng số điện tho<PERSON>i không hợp lệ", "update-user-info.email.placeholder": "Nhập email", "update-user-info.phoneNumber.placeholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "function.email-log.table.toEmail": "<PERSON><PERSON><PERSON><PERSON>n", "function.email-log.table.status": "<PERSON>r<PERSON><PERSON> thái g<PERSON>i", "function.email-log.table.action": "<PERSON><PERSON><PERSON> đ<PERSON>", "function.email-log.table.createdDate": "<PERSON><PERSON><PERSON>", "function.email-log.table.email-subject": "Ti<PERSON><PERSON> đ<PERSON> email", "function.email-log.table.email-template": "Mẫu email", "function.email-log.table.detail.subject": "<PERSON><PERSON>i dung", "function.email-log.table.traceId": "Trace Id", "function.email-log.table.detail.error-message": "<PERSON>h<PERSON>ng báo lỗi", "function.email-log.grid.email-template-code.label": "Mẫu email", "function.email-log.grid.email-template-code.placeholder": "Mẫu email", "function.email-log.grid.created-date.label": "<PERSON><PERSON><PERSON>", "function.forgot-password-log.table.userName": "Username", "function.forgot-password-log.table.userEmail": "Email", "function.forgot-password-log.table.status": "<PERSON><PERSON> cập nhật mật khẩu", "function.forgot-password-log.table.createdDate": "<PERSON><PERSON><PERSON>", "function.forgot-password-log.table.traceId": "Trace Id", "function.forgot-password-log.grid.username.label": "Username", "function.forgot-password-log.grid.username.placeholder": "Username", "function.forgot-password-log.grid.created-date.label": "<PERSON><PERSON><PERSON>", "menu.chi-tieu-tuyen-sinh": "Chỉ tiêu tuyển sinh", "menu.title-chi-tieu-tuyen-sinh": "Chỉ tiêu tuyển sinh", "function.chi-tieu-tuyen-sinh.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.chi-tieu-tuyen-sinh.table.nam-tuyen-sinh": "Năm TS", "function.chi-tieu-tuyen-sinh.table.ten-he": "<PERSON><PERSON><PERSON>", "function.chi-tieu-tuyen-sinh.table.ten-nganh": "<PERSON><PERSON><PERSON>", "function.chi-tieu-tuyen-sinh.table.chi-tieu-tuyen-sinh": "Chỉ tiêu TS", "function.chi-tieu-tuyen-sinh.table.diem-san-xet-tuyen": "<PERSON><PERSON><PERSON><PERSON> sàn x<PERSON>t tuyển", "function.chi-tieu-tuyen-sinh.modal.form.nam-tuyen-sinh": "Năm TS ", "function.chi-tieu-tuyen-sinh.modal.form.nam-tuyen-sinh.required": "Năm TS không được để trống", "function.chi-tieu-tuyen-sinh.modal.form.nam-tuyen-sinh.place-holder": "Năm TS", "function.chi-tieu-tuyen-sinh.modal.form.diem-san-xet-tuyen": "<PERSON><PERSON><PERSON><PERSON> sàn x<PERSON>t tuyển", "function.chi-tieu-tuyen-sinh.modal.form.diem-san-xet-tuyen.required": "<PERSON><PERSON><PERSON><PERSON> sàn xét tuyển không đư<PERSON><PERSON> để trống", "function.chi-tieu-tuyen-sinh.modal.form.diem-san-xet-tuyen.place-holder": "<PERSON><PERSON><PERSON><PERSON> sàn x<PERSON>t tuyển", "function.chi-tieu-tuyen-sinh.modal.form.ten-he": "<PERSON><PERSON>", "function.chi-tieu-tuyen-sinh.modal.form.ten-he.required": "<PERSON><PERSON> không đư<PERSON><PERSON> để trống", "function.chi-tieu-tuyen-sinh.modal.form.ten-he.place-holder": "<PERSON><PERSON>", "function.chi-tieu-tuyen-sinh.modal.form.ten-nganh": "<PERSON><PERSON><PERSON> ", "function.chi-tieu-tuyen-sinh.modal.form.ten-nganh.required": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "function.chi-tieu-tuyen-sinh.modal.form.ten-nganh.place-holder": "<PERSON><PERSON><PERSON>", "function.chi-tieu-tuyen-sinh.modal.form.chi-tieu-tuyen-sinh": "Chỉ tiêu TS ", "function.chi-tieu-tuyen-sinh.modal.form.chi-tieu-tuyen-sinh.required": "Chỉ tiêu TS không được để trống", "function.chi-tieu-tuyen-sinh.modal.form.chi-tieu-tuyen-sinh.place-holder": "Chỉ tiêu TS ", "function.chi-tieu-tuyen-sinh.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.chi-tieu-tuyen-sinh.confirm-delete.content": "Bạn có chắc chắn muốn xóa chỉ tiêu tuyển sinh này", "function.chi-tieu-tuyen-sinh.modal.title-add": "Thêm chỉ tiêu tuyển sinh", "function.chi-tieu-tuyen-sinh.modal.title-info": "<PERSON>em chi tiết chỉ tiêu tuyển sinh", "function.chi-tieu-tuyen-sinh.modal.title-edit": "<PERSON><PERSON>a chỉ tiêu tuyển sinh", "function.chi-tieu-tuyen-sinh.page.title": "<PERSON><PERSON> sách chỉ tiêu tuyển sinh", "function.danh-sach-lop-quan-ly.modal.title": "<PERSON><PERSON> s<PERSON>ch lớp quản lý", "function.danh-sach-lop-quan-ly.table.ten-he": "<PERSON><PERSON> đào tạo", "function.danh-sach-lop-quan-ly.table.ten-khoa": "<PERSON><PERSON><PERSON>", "function.danh-sach-lop-quan-ly.table.khoa-hoc": "<PERSON><PERSON><PERSON><PERSON>", "function.danh-sach-lop-quan-ly.table.ten-nganh": "<PERSON><PERSON><PERSON>", "function.danh-sach-lop-quan-ly.table.ten-chuyen-nganh": "<PERSON><PERSON><PERSON><PERSON>", "function.danh-sach-lop-quan-ly.table.ten-lop": "<PERSON><PERSON><PERSON>", "function.danh-sach-lop-quan-ly.table.ra-truong": "<PERSON> trường", "he.form.he": "<PERSON><PERSON>", "he.form.he.place-holder": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "khoa.form.khoa": "<PERSON><PERSON><PERSON>", "khoa.form.khoa.place-holder": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>a", "khoa-hoc.form.khoa-hoc": "<PERSON><PERSON><PERSON><PERSON>", "khoa-hoc.form.khoa-hoc.place-holder": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "nganh.form.nganh": "<PERSON><PERSON><PERSON>", "nganh.form.nganh.place-holder": "<PERSON><PERSON><PERSON><PERSON>", "chuyen-nganh.form.chuyen-nganh": "<PERSON><PERSON><PERSON><PERSON>", "chuyen-nganh.form.chuyen-nganh.place-holder": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>nh", "function.danh-sach-lop-quan-ly.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.danh-sach-lop-quan-ly.confirm-delete.content": "Bạn có chắc chắn muốn xóa", "them.model.button": "<PERSON><PERSON><PERSON><PERSON>", "menu.title-benh-vien": "Bệnh viện", "function.benh-vien.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.benh-vien.table.maBenhVien": "<PERSON><PERSON> b<PERSON>nh viện", "function.benh-vien.table.tenBenhVien": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> viện", "function.benh-vien.table.tuyenBvTruoc2025": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> viện trước 01/01/2025", "function.benh-vien.table.dangKyKcbBanDau": "Đăng ký KCB ban đầu", "function.benh-vien.table.diaChi": "Địa chỉ", "function.benh-vien.table.ghiChu": "<PERSON><PERSON><PERSON>", "function.benh-vien.page.title": "<PERSON><PERSON> s<PERSON> b<PERSON><PERSON> viện", "function.benh-vien.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.benh-vien.confirm-delete.content": "<PERSON><PERSON>n có chắc chắn muốn xóa bệnh viện:", "function.benh-vien.modal.form.maBenhVien": "<PERSON><PERSON> b<PERSON>nh viện", "function.benh-vien.modal.form.maBenhVien.required": "<PERSON><PERSON> bệnh viện không được để trống", "function.benh-vien.modal.form.maBenhVien.place-holder": "<PERSON><PERSON> b<PERSON>nh viện ", "function.benh-vien.modal.form.tenBenhVien": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> viện", "function.benh-vien.modal.form.tenBenhVien.required": "<PERSON><PERSON><PERSON> bệnh viện không được để trống", "function.benh-vien.modal.form.tenBenhVien.place-holder": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> viện", "function.benh-vien.modal.form.tuyenBvTruoc2025": "<PERSON><PERSON><PERSON><PERSON> bện viện trước 01/01/2025", "function.benh-vien.modal.form.tuyenBvTruoc2025.required": "<PERSON><PERSON><PERSON><PERSON> bện viện trước 01/01/2025 không được để trống", "function.benh-vien.modal.form.tuyenBvTruoc2025.place-holder": "<PERSON><PERSON><PERSON><PERSON> bện viện trước 01/01/2025", "function.benh-vien.modal.form.dangKyKcbBanDau": "Đăng ký KCB ban đầu", "function.benh-vien.modal.form.dangKyKcbBanDau.required": "Đ<PERSON>ng ký KCB ban đầu không đư<PERSON><PERSON> để trống", "function.benh-vien.modal.form.dangKyKcbBanDau.place-holder": "Đăng ký KCB ban đầu", "function.benh-vien.modal.form.diaChi": "Địa chỉ", "function.benh-vien.modal.form.diaChi.place-holder": "Địa chỉ", "function.benh-vien.modal.form.ghiChu": "<PERSON><PERSON><PERSON>", "function.benh-vien.modal.form.ghiChu.place-holder": "<PERSON><PERSON><PERSON>", "function.benh-vien.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> mới b<PERSON>nh viện", "function.benh-vien.modal.title-edit": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> b<PERSON><PERSON> viện", "function.benh-vien.modal.title-info": "<PERSON> ti<PERSON>t b<PERSON><PERSON> viện", "menu.title-phuong-an": "<PERSON><PERSON><PERSON><PERSON>", "function.phuong-an.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.phuong-an.table.maPhuongAn": "<PERSON>ã phư<PERSON>ng án", "function.phuong-an.table.tenPhuongAn": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON>n", "function.phuong-an.table.noiDung": "<PERSON><PERSON>i dung", "function.phuong-an.page.title": "<PERSON><PERSON> s<PERSON>ch ph<PERSON><PERSON><PERSON> án", "function.phuong-an.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.phuong-an.confirm-delete.content": "Bạn có chắc chắn muốn xóa phương án:", "function.phuong-an.modal.form.maPhuongAn": "<PERSON>ã phư<PERSON>ng án", "function.phuong-an.modal.form.maPhuongAn.required": "<PERSON><PERSON> phương án không được để trống", "function.phuong-an.modal.form.maPhuongAn.place-holder": "<PERSON>ã phư<PERSON>ng án ", "function.phuong-an.modal.form.tenPhuongAn": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON>n", "function.phuong-an.modal.form.tenPhuongAn.required": "Tên phương án không được để trống", "function.phuong-an.modal.form.tenPhuongAn.place-holder": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON>n", "function.phuong-an.modal.form.noiDung": "<PERSON><PERSON>i dung", "function.phuong-an.modal.form.noiDung.place-holder": "<PERSON><PERSON>i dung", "function.phuong-an.modal.title-add": "<PERSON>h<PERSON><PERSON> mới ph<PERSON><PERSON>ng án", "function.phuong-an.modal.title-edit": "<PERSON><PERSON><PERSON> nh<PERSON>t ph<PERSON><PERSON><PERSON>n", "function.phuong-an.modal.title-info": "<PERSON> tiết ph<PERSON><PERSON><PERSON> án", "menu.title-phuong-thuc-dong": "<PERSON><PERSON><PERSON><PERSON> thức đ<PERSON>g", "function.phuong-thuc-dong.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.phuong-thuc-dong.table.phuongThucDong": "<PERSON><PERSON><PERSON><PERSON> thức đ<PERSON>g", "function.phuong-thuc-dong.table.ghiChu": "<PERSON><PERSON><PERSON>", "function.phuong-thuc-dong.page.title": "<PERSON><PERSON> s<PERSON> thức đ<PERSON>g", "function.phuong-thuc-dong.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.phuong-thuc-dong.confirm-delete.content": "<PERSON>ạn có chắc chắn muốn xóa <PERSON><PERSON> thức đóng:", "function.phuong-thuc-dong.modal.form.phuongThucDong": "<PERSON><PERSON><PERSON><PERSON> thức đ<PERSON>g", "function.phuong-thuc-dong.modal.form.phuongThucDong.required": "<PERSON><PERSON><PERSON><PERSON> thức đóng không được để trống", "function.phuong-thuc-dong.modal.form.phuongThucDong.place-holder": "<PERSON><PERSON><PERSON><PERSON> thức đ<PERSON>g", "function.phuong-thuc-dong.modal.form.ghiChu": "<PERSON><PERSON><PERSON>", "function.phuong-thuc-dong.modal.form.ghiChu.place-holder": "<PERSON><PERSON><PERSON>", "function.phuong-thuc-dong.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> mới <PERSON> thức đóng", "function.phuong-thuc-dong.modal.title-edit": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON><PERSON> thức đóng", "function.phuong-thuc-dong.modal.title-info": "<PERSON> tiết <PERSON><PERSON><PERSON> thức đóng", "menu.title-vung": "<PERSON><PERSON><PERSON>", "function.vung.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.vung.table.kyHieu": "<PERSON><PERSON>", "function.vung.table.tenVung": "<PERSON><PERSON><PERSON>", "function.vung.table.ghiChu": "<PERSON><PERSON><PERSON>", "function.vung.page.title": "<PERSON><PERSON>", "function.vung.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.vung.confirm-delete.content": "Bạn có chắc chắn muốn xóa <PERSON>:", "function.vung.modal.form.kyHieu": "<PERSON><PERSON>", "function.vung.modal.form.kyHieu.required": "<PERSON><PERSON> hiệu không đư<PERSON><PERSON> để trống", "function.vung.modal.form.kyHieu.place-holder": "<PERSON><PERSON> ", "function.vung.modal.form.tenVung": "<PERSON><PERSON><PERSON>", "function.vung.modal.form.tenVung.required": "<PERSON><PERSON><PERSON> không được để trống", "function.vung.modal.form.tenVung.place-holder": "<PERSON><PERSON><PERSON>", "function.vung.modal.form.ghiChu": "<PERSON><PERSON><PERSON>", "function.vung.modal.form.ghiChu.place-holder": "<PERSON><PERSON><PERSON>", "function.vung.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> mới <PERSON>", "function.vung.modal.title-edit": "<PERSON><PERSON><PERSON><PERSON>", "function.vung.modal.title-info": "<PERSON> ti<PERSON>t <PERSON>", "menu.title-muc-huong-bhyt": "Mức hưởng BHYT", "function.muc-huong-bhyt.search-box.placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "function.muc-huong-bhyt.table.kyHieu": "<PERSON><PERSON>", "function.muc-huong-bhyt.table.doiTuongMucHuong": "<PERSON><PERSON><PERSON> tư<PERSON> mức hưởng", "function.muc-huong-bhyt.table.mucHuong": "<PERSON><PERSON><PERSON> hưởng", "function.muc-huong-bhyt.page.title": "<PERSON><PERSON> s<PERSON>ch mức hưởng BHYT", "function.muc-huong-bhyt.confirm-delete.title": "<PERSON><PERSON><PERSON>n x<PERSON>a", "function.muc-huong-bhyt.confirm-delete.content": "Bạn có chắc chắn muốn xóa mức hưởng BHYT:", "function.muc-huong-bhyt.modal.form.kyHieu": "<PERSON><PERSON>", "function.muc-huong-bhyt.modal.form.kyHieu.required": "<PERSON><PERSON> hiệu không đư<PERSON><PERSON> để trống", "function.muc-huong-bhyt.modal.form.kyHieu.place-holder": "<PERSON><PERSON> ", "function.muc-huong-bhyt.modal.form.doiTuongMucHuong": "<PERSON><PERSON><PERSON> tư<PERSON> mức hưởng", "function.muc-huong-bhyt.modal.form.doiTuongMucHuong.required": "<PERSON><PERSON><PERSON> tượng mức hưởng không được để trống", "function.muc-huong-bhyt.modal.form.doiTuongMucHuong.place-holder": "<PERSON><PERSON><PERSON> tư<PERSON> mức hưởng", "function.muc-huong-bhyt.modal.form.mucHuong": "<PERSON><PERSON><PERSON> hưởng", "function.muc-huong-bhyt.modal.form.mucHuong.place-holder": "<PERSON><PERSON><PERSON> hưởng", "function.muc-huong-bhyt.modal.title-add": "<PERSON><PERSON><PERSON><PERSON> mới mức hưởng BHYT", "function.muc-huong-bhyt.modal.title-edit": "<PERSON><PERSON><PERSON> nh<PERSON>t mức hưởng BHYT", "function.muc-huong-bhyt.modal.title-info": "<PERSON> tiết mức hưởng BHYT", "workflow-map-function.table.ten-chuc-nang": "<PERSON><PERSON><PERSON> n<PERSON>", "workflow-map-function.table.phan-he": "<PERSON><PERSON>", "workflow-map-function.table.gia-tri": "<PERSON>ên quy trình", "workflow-map-function.table.active": "<PERSON><PERSON><PERSON><PERSON> thái", "workflow-map-function-item.table.active": "Sử dụng", "workflow-map-function.table.date-modify": "<PERSON><PERSON><PERSON>", "menu.workflow-function": "<PERSON><PERSON> quy trình - ch<PERSON><PERSON> n<PERSON>ng", "workflow-map-function.table.gia-tri.required": "Tên quy trình không được để trống", "workflow-map-function.table.gia-tri.placehoder": "<PERSON><PERSON><PERSON> chọn tên quy trình", "function.workflow-map-function.page.title": "<PERSON><PERSON> quy trình chức n<PERSON>ng"}