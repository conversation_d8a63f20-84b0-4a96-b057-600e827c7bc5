import { Injectable } from '@angular/core';
import { environment } from '@env/environment';
import { getBaseClientUrl } from 'src/app/shared-ui/utils/shared-utils';

@Injectable({
  providedIn: 'root'
})
export class KeycloakService {
  private clientUrl = '';

  constructor() {
    this.clientUrl = getBaseClientUrl();
  }

  /**
   * Redirect to KeyCloak login page
   */
  public login(): void {
    const keycloakConfig = environment['keycloakServer'];
    if (!keycloakConfig?.baseUrl || !keycloakConfig?.realm || !keycloakConfig?.clientId) {
      console.error('KeyCloak configuration is missing');
      return;
    }

    const authUrl = this.buildAuthUrl();
    window.location.href = authUrl;
  }

  /**
   * Build KeyCloak OAuth2 authorization URL
   */
  private buildAuthUrl(): string {
    const keycloakConfig = environment['keycloakServer'];
    const redirectUri = `${this.clientUrl}/oidc-callback`;

    const params = new URLSearchParams({
      client_id: keycloakConfig.clientId,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope: keycloakConfig.scopes || 'openid profile email',
      state: this.generateState()
    });

    return `${keycloakConfig.baseUrl}/realms/${keycloakConfig.realm}/protocol/openid-connect/auth?${params.toString()}`;
  }

  /**
   * Handle KeyCloak logout
   */
  public logout(): void {
    const keycloakConfig = environment['keycloakServer'];
    if (!keycloakConfig?.baseUrl || !keycloakConfig?.realm) {
      console.error('KeyCloak configuration is missing');
      return;
    }

    const logoutUrl = this.buildLogoutUrl();
    window.location.href = logoutUrl;
  }

  /**
   * Build KeyCloak logout URL
   */
  private buildLogoutUrl(): string {
    const keycloakConfig = environment['keycloakServer'];
    const redirectUri = `${this.clientUrl}`;

    const params = new URLSearchParams({
      redirect_uri: redirectUri
    });

    return `${keycloakConfig.baseUrl}/realms/${keycloakConfig.realm}/protocol/openid-connect/logout?${params.toString()}`;
  }

  /**
   * Generate random state for OAuth2 security
   */
  private generateState(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * Get KeyCloak token endpoint URL
   */
  public getTokenEndpoint(): string {
    const keycloakConfig = environment['keycloakServer'];
    return `${keycloakConfig.baseUrl}/realms/${keycloakConfig.realm}/protocol/openid-connect/token`;
  }

  /**
   * Get KeyCloak userinfo endpoint URL
   */
  public getUserInfoEndpoint(): string {
    const keycloakConfig = environment['keycloakServer'];
    return `${keycloakConfig.baseUrl}/realms/${keycloakConfig.realm}/protocol/openid-connect/userinfo`;
  }
}
