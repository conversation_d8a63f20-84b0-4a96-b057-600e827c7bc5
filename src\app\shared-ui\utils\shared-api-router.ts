export const signalRRouter = {
  hubUrl: `/signalr_hub`
};

export const authenticationRouter = {
  loginJWT: `/auth/v1/authentication/jwt/login`,
  changePassword: `/auth/v1/authentication/change-password`,
  updatePasswordForgotByUser: `/auth/v1/authentication/forgot/update-password`,
  getAccessToken: `/auth/v1/oidc/access-token?_allow_anonymous=true`,
  refreshToken: `/auth/v1/oidc/refresh-token`,
  revokeToken: `/auth/v1/oidc/revoke-token`,
  getUserInfo: `/auth/v1/oidc/user-info`,
  introspectToken: `/auth/v1/oidc/introspect-token`,
  validateHouTicket: `/auth/v1/hou/validate-ticket`
};

export const cacheRouter = {
  removeAllCache: '/system/v1/cache/clean-all-cache'
};

export const sharedUserRouter = {
  updateUserInfo: '/system/v1/user/update-user-info',
  getCurrentUser: '/system/v1/user/get-current-user',
  getUserPermission: `/system/v1/user/get-permission-current-user`,
  getUserAccessLop: `/system/v1/user/get-access-lop-current-user`
};
export const boMonRouter = {
  getCombobox: `/system/v1/bo-mon/for-combobox`,
  getListBoMon: `/system/v1/bo-mon/filter`,
  getGvThuocBoMon: `/system/v1/bo-mon/giang-vien-filter`,
  getGvChuaGanBoMon: `/system/v1/bo-mon/giang-vien-chua-gan-filter`,
  getMonHocThuocBoMon: `/system/v1/bo-mon/mon-hoc-filter`,
  getMonHocChuaGanBoMon: `/system/v1/bo-mon/mon-hoc-chua-gan-filter`,
  createBoMon: `/system/v1/bo-mon`,
  updateBoMon: `/system/v1/bo-mon/`,
  getById: `/system/v1/bo-mon/`,
  deleteListBoMon: `/system/v1/bo-mon/delete-many`,
  createGiangVienBoMon: `/system/v1/bo-mon/create-bo-mon-giang-vien`,
  deleteGiangVienBoMon: `/system/v1/bo-mon/delete-bo-mon-giang-vien`,
  updateMonHocBoMon: `/system/v1/bo-mon/update-mon-hoc-bo-mon`
};

export const monHocRouter = {
  getFilter: `/system/v1/mon-hoc/filter`,
  create: `/system/v1/mon-hoc`,
  update: `/system/v1/mon-hoc/`,
  delete: `/system/v1/mon-hoc/`,
  getCombobox: `/system/v1/mon-hoc/for-combobox`
};
export const heRouter = {
  getCombobox: `/system/v1/he/for-combobox`
};

export const permissionRouter = {
  getListCombobox: `/system/v1/permission/for-combobox`
};

export const workflowRouter = {
    getHistoryWorkflow: `/system/v1/workflow/history/`
};
