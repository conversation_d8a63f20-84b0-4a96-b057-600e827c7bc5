<page-header-wrapper [title]="title" onBack="{() => window.history.back()}">
  <nz-card [nzBordered]="false">
    <nz-row>
      <nz-col nzSm="16" nzXS="24" nzMd="16" class="padding-bottom-10">
        <button
          nz-button
          nzType="default"
          (click)="btnReload.click($event)"
          class="btn-reload"
          *ngIf="btnReload.visible && btnReload.grandAccess"
        >
          <i nz-icon nzType="reload" nzTheme="outline"></i>{{ btnReload.title }}
        </button>
        <button nz-button nzType="primary" (click)="btnAdd.click($event)" class="btn-primary" *ngIf="btnAdd.visible && btnAdd.grandAccess">
          <i nz-icon nzType="file-add" nzTheme="fill"></i>{{ btnAdd.title }}
        </button>
        <button
          nz-button
          nzType="default"
          class="btn-danger"
          (click)="btnDelete.click($event)"
          nzDanger
          *ngIf="btnDelete.visible && btnDelete.grandAccess"
        >
          <i nz-icon nzType="delete" nzTheme="fill"></i>{{ btnDelete.title }}
        </button>
      </nz-col>
      <nz-col nzSm="8" nzXS="24" nzMd="8" class="pull-right padding-bottom-10">
        <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
          <input
            type="text"
            [(ngModel)]="filter.textSearch"
            nz-input
            placeholder="{{ 'function.department.search-box.placeholder' | i18n }}"
            (keyup.enter)="initGridData()"
          />
        </nz-input-group>
        <ng-template #suffixIconButton>
          <button nz-button nzType="default" nzSearch (click)="initGridData()"><span nz-icon nzType="search"></span></button>
        </ng-template>
      </nz-col>
    </nz-row>
    <nz-row>
      <ag-grid-angular
        #agGrid
        style="width: 100%; height: 70vh"
        id="application-grid"
        class="ag-theme-alpine"
        [columnDefs]="columnDefs"
        [defaultColDef]="defaultColDef"
        [suppressRowClickSelection]="true"
        rowSelection="multiple"
        [rowData]="grid.rowData"
        (selectionChanged)="onSelectionChanged($event)"
        (cellDoubleClicked)="onCellDoubleClicked($event)"
        [overlayLoadingTemplate]="overlayLoadingTemplate"
        [overlayNoRowsTemplate]="overlayNoRowsTemplate"
        [components]="frameworkComponents"
        [excelStyles]="excelStyles"
        (gridReady)="onGridReady($event)"
      >
      </ag-grid-angular>
      <hr />
    </nz-row>
    <app-ag-grid-pagination
      [grid]="grid"
      [filter]="filter"
      [pageSizeOptions]="pageSizeOptions"
      (pageNumberChange)="onPageNumberChange()"
      (pageSizeChange)="onPageSizeChange()"
    ></app-ag-grid-pagination>
  </nz-card>
</page-header-wrapper>

<app-khoa-item
  #itemModal
  [isVisible]="modal.isShow"
  [item]="modal.item"
  [type]="modal.type"
  [option]="modal.option"
  (eventEmmit)="onModalEventEmmit($event)"
>
</app-khoa-item>
