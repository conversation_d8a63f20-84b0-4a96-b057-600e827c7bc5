// <PERSON><PERSON>u các tham số dùng chung
import { Menu } from '@delon/theme';
import { QueryFilerModel } from '@model';

export const SETTING_KEY_BUILD = `build`;

export const MENU_CONSTANTS: Menu[] = [
  {
    text: 'Trang chủ',
    i18n: 'menu.main',
    group: true,
    hideInBreadcrumb: true,
    children: [
      {
        text: 'Trang chủ',
        i18n: 'menu.dashboard',
        icon: 'anticon anticon-dashboard',
        link: '/dashboard'
      },
      {
        text: 'Nhật ký',
        i18n: 'menu.log',
        icon: 'anticon anticon-history',
        link: '/sys/log',
        acl: ['NHAT_KY_HE_THONG_VIEW', 'FORGOT_PASSWORD_LOG_VIEW', 'SEND_MAIL_LOG_VIEW'],
        children: [
          {
            text: 'Nhật ký hệ thống',
            i18n: 'menu.system-log',
            link: '/sys/log',
            acl: ['NHAT_KY_HE_THONG_VIEW']
          },
          {
            text: 'Nh<PERSON>t ký quên mật khẩu',
            i18n: 'menu.forgot-password-log',
            link: '/sys/forgot-password-log',
            acl: ['FORGOT_PASSWORD_LOG_VIEW']
          },
          {
            text: 'Nhật ký gửi mail',
            i18n: 'menu.email-log',
            link: '/sys/email-log',
            acl: ['SEND_MAIL_LOG_VIEW']
          }
        ]
      },
      {
        text: 'Phân quyền',
        i18n: 'menu.permission',
        icon: 'anticon anticon-appstore',
        acl: ['QUAN_LY_NGUOI_DUNG_VIEW', 'NHOM_NGUOI_DUNG_VIEW'],
        children: [
          {
            text: 'Người dùng',
            i18n: 'menu.user',
            link: '/sys/user',
            acl: ['QUAN_LY_NGUOI_DUNG_VIEW']
          },
          {
            text: 'Nhóm người dùng',
            i18n: 'menu.role',
            link: '/sys/role',
            acl: ['NHOM_NGUOI_DUNG_VIEW']
          }
        ]
      },
      {
        text: 'Cấu hình',
        i18n: 'menu.setting',
        icon: 'anticon anticon-setting',
        acl: ['EMAIL_TEMPLATE_VIEW'],
        children: [
          {
            text: 'Email template',
            i18n: 'menu.email-template',
            link: '/sys/email-template',
            acl: ['EMAIL_TEMPLATE_VIEW']
          },
          {
            text: 'Quy trình',
            i18n: 'menu.workflow',
            link: '/sys/workflow',
            acl: ['WORKFLOW_VIEW']
          },
          {
            text: 'Gán quy trình - chức năng',
            i18n: 'menu.workflow-function',
            link: '/sys/workflow/map-function',
            acl: ['WORKFLOW_VIEW']
          }
        ]
      },
      {
        text: 'Danh mục',
        i18n: 'menu.catalog',
        icon: 'anticon anticon-setting',
        acl: [
          'HE_VIEW',
          'TON_GIAO_VIEW',
          'QUOC_TICH_VIEW',
          'DAN_TOC_VIEW',
          'GIOI_TINH_VIEW',
          'KHOA_VIEW',
          'NGANH_VIEW',
          'CHUYEN_NGANH_VIEW',
          'TINH',
          'HUYEN',
          'KHU_VUC',
          'NHOM_DOI_TUONG',
          'DOI_TUONG',
          'DOI_TUONG_HOC_BONG',
          'CAP_KHEN_THUONG_KY_LUAT_VIEW'
        ],
        children: [
          {
            text: 'Hệ đào tạo',
            i18n: 'menu.training-system',
            link: '/sys/he-dao-tao',
            acl: ['HE_VIEW']
          },
          {
            text: 'Tôn giáo',
            i18n: 'menu.religion',
            link: '/sys/ton-giao',
            acl: ['TON_GIAO_VIEW']
          },
          {
            text: 'Quốc tịch',
            i18n: 'menu.nationality',
            link: '/sys/quoc-tich',
            acl: ['TON_GIAO_VIEW']
          },
          {
            text: 'Dân tộc',
            i18n: 'menu.ethnic',
            link: '/sys/dan-toc',
            acl: ['DAN_TOC_VIEW']
          },
          {
            text: 'Giới tính',
            i18n: 'menu.gender',
            link: '/sys/gioi-tinh',
            acl: ['GIOI_TINH_VIEW']
          },
          {
            text: 'Học vị',
            i18n: 'menu.degree',
            link: '/sys/hoc-vi',
            acl: ['HOC_VI_VIEW']
          },
          {
            text: 'Học hàm',
            i18n: 'menu.academic-rank',
            link: '/sys/hoc-ham',
            acl: ['HOC_HAM_VIEW']
          },
          {
            text: 'Khoa',
            i18n: 'menu.department',
            link: '/sys/khoa',
            acl: ['KHOA_VIEW']
          },
          {
            text: 'Ngành',
            i18n: 'menu.industry',
            link: '/sys/nganh',
            acl: ['NGANH_VIEW']
          },
          {
            text: 'Chuyên ngành',
            i18n: 'menu.majors',
            link: '/sys/chuyen-nganh',
            acl: ['CHUYEN_NGANH_VIEW']
          },
          {
            text: 'Tỉnh',
            i18n: 'menu.province',
            link: '/sys/tinh',
            acl: ['TINH_VIEW']
          },
          {
            text: 'Huyện',
            i18n: 'menu.district',
            link: '/sys/huyen',
            acl: ['HUYEN_VIEW']
          },
          {
            text: 'Khu Vực',
            i18n: 'menu.area',
            link: '/sys/khu-vuc',
            acl: ['KHU_VUC_VIEW']
          },
          {
            text: 'Nhóm đối tượng',
            i18n: 'menu.object-group',
            link: '/sys/nhom-doi-tuong',
            acl: ['NHOM_DOI_TUONG_VIEW']
          },
          {
            text: 'Đối tượng',
            i18n: 'menu.object',
            link: '/sys/doi-tuong',
            acl: ['DOI_TUONG_VIEW']
          },
          {
            text: 'Đối tượng học bổng',
            i18n: 'menu.scholarship-object',
            link: '/sys/doi-tuong-hoc-bong',
            acl: ['DOI_TUONG_HOC_BONG_VIEW']
          },
          {
            text: 'Cấp khen thưởng kỷ luật',
            i18n: 'menu.lever',
            link: '/sys/cap-khen-thuong-ky-luat',
            acl: ['CAP_KHEN_THUONG_KY_LUAT_VIEW']
          },
          {
            text: 'Loại khen thưởng',
            i18n: 'menu.reward-type',
            link: '/sys/loai-khen-thuong',
            acl: ['LOAI_KHEN_THUONG_VIEW']
          },
          {
            text: 'Hành vi',
            i18n: 'menu.behavior',
            link: '/sys/hanh-vi',
            acl: ['HANH_VI_VIEW']
          },
          {
            text: 'Xử lý',
            i18n: 'menu.handle',
            link: '/sys/xu-ly',
            acl: ['XU_LY_VIEW']
          },
          {
            text: 'Chức danh',
            i18n: 'menu.title',
            link: '/sys/chuc-danh',
            acl: ['CHUC_DANH_VIEW']
          },
          {
            text: 'Xếp loại rèn luyện',
            i18n: 'menu.rating',
            link: '/sys/xep-loai-ren-luyen',
            acl: ['XEP_LOAI_REN_LUYEN_VIEW']
          },
          {
            text: 'Loại rèn luyện',
            i18n: 'menu.type-of-training',
            link: '/sys/loai-ren-luyen',
            acl: ['LOAI_REN_LUYEN_VIEW']
          },
          {
            text: 'Loại thành phần điểm',
            i18n: 'menu.loai-thanh-phan-diem',
            link: '/sys/loai-thanh-phan-diem',
            acl: ['LOAI_DIEM_THANH_PHAN_VIEW']
          },
          {
            text: 'Xếp loại học tập thang 10',
            i18n: 'menu.xep-loai-hoc-tap-thang-10',
            link: '/sys/xep-loai-hoc-tap-thang-10',
            acl: ['XEP_LOAI_HOC_TAP_THANG_DIEM_10_VIEW']
          },
          {
            text: 'Xếp loại học lực',
            i18n: 'menu.xep-hang-hoc-luc',
            link: '/sys/xep-hang-hoc-luc',
            acl: ['XEP_HANG_HOC_LUC_VIEW']
          },
          {
            text: 'Xếp hạng năm đào tạo',
            i18n: 'menu.xep-hang-nam-dao-tao',
            link: '/sys/xep-hang-nam-dao-tao',
            acl: ['XEP_HANG_NAM_DAO_TAO_VIEW']
          },
          {
            text: 'Xếp loại học tập thang 4',
            i18n: 'menu.xep-loai-hoc-luc-thang-4',
            link: '/sys/xep-loai-hoc-tap-thang-4',
            acl: ['XEP_LOAI_HOC_TAP_THANG_DIEM_10_VIEW']
          },
          {
            text: 'Xếp loại tôt nghiệp thang 4',
            i18n: 'menu.xep-loai-tot-nghiep-thang-4',
            link: '/sys/xep-loai-tot-nghiep-thang-4',
            acl: ['XEP_LOAI_TOT_NGHIEP_THANG_4_VIEW']
          },
          {
            text: 'Xếp loại tôt nghiệp thang 10',
            i18n: 'menu.xep-loai-tot-nghiep-thang-10',
            link: '/sys/xep-loai-tot-nghiep-thang-10',
            acl: ['XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_VIEW']
          },
          {
            text: 'Loại chứng chỉ',
            i18n: 'menu.loai-chung-chi',
            link: '/sys/loai-chung-chi',
            acl: ['LOAI_CHUNG_CHI_VIEW']
          },
          {
            text: 'Xếp loại chứng chỉ',
            i18n: 'menu.xep-loai-chung-chi',
            link: '/sys/xep-loai-chung-chi',
            acl: ['XEP_LOAI_CHUNG_CHI_VIEW']
          },
          {
            text: 'Xếp loại chứng chỉ',
            i18n: 'menu.nhom-chung-chi',
            link: '/sys/nhom-chung-chi',
            acl: ['NHOM_CHUNG_CHI_VIEW']
          },
          {
            text: 'Loại giấy tờ',
            i18n: 'menu.title-loai-giay-to',
            link: '/sys/loai-giay-to',
            acl: ['LOAI_GIAY_TO_VIEW']
          },
          {
            text: 'Học kỳ đăng ký',
            i18n: 'menu.title-hoc-ky-dang-ky',
            link: '/sys/hoc-ky-dang-ky',
            acl: ['HOC_KY_DANG_KY_VIEW']
          },
          {
            text: 'Phường ngoại trú',
            i18n: 'menu.title-phuong-ngoai-tru',
            link: '/sys/phuong-ngoai-tru',
            acl: ['PHUONG_VIEW']
          },
          {
            text: 'Xã',
            i18n: 'menu.title-xa',
            link: '/sys/xa',
            acl: ['XA_VIEW']
          },
          {
            text: 'Hình thức học',
            i18n: 'menu.title-hinh-thuc-hoc',
            link: '/sys/hinh-thuc-hoc',
            acl: ['HINH_THUC_HOC_VIEW']
          },
          {
            text: 'Hình thức thi',
            i18n: 'menu.title-hinh-thuc-thi',
            link: '/sys/hinh-thuc-thi',
            acl: ['HINH_THUC_THI_VIEW']
          },
          {
            text: 'Xếp loại học bổng',
            i18n: 'menu.title-xep-loai-hoc-bong',
            link: '/sys/xep-loai-hoc-bong',
            acl: ['XEP_LOAI_HOC_BONG_VIEW']
          },
          {
            text: 'Tòa nhà',
            i18n: 'menu.title-toa-nha',
            link: '/sys/toa-nha',
            acl: ['TOA_NHA_VIEW']
          },
          {
            text: 'Đối tượng học phí',
            i18n: 'menu.title-doi-tuong-hoc-phi',
            link: '/sys/doi-tuong-hoc-phi',
            acl: ['DOI_TUONG_HOC_PHI_VIEW']
          },
          {
            text: 'Bộ môn',
            i18n: 'menu.title-bo-mon',
            link: '/sys/bo-mon',
            acl: ['BO_MON_VIEW']
          },
          {
            text: 'Cơ sở đào tạo',
            i18n: 'menu.title-co-so-dao-tao',
            link: '/sys/co-so-dao-tao',
            acl: ['CO_SO_DAO_TAO_VIEW']
          },
          {
            text: 'Điểm rèn luyện quy đổi',
            i18n: 'menu.title-diem-ren-luyen-quy-doi',
            link: '/sys/diem-ren-luyen-quy-doi',
            acl: ['DIEM_REN_LUYEN_QUY_DOI_VIEW']
          },
          {
            text: 'Đơn vị thực tập',
            i18n: 'menu.title-don-vi-thuc-tap',
            link: '/sys/don-vi-thuc-tap',
            acl: ['NOI_THUC_TAP_VIEW']
          },
          {
            text: 'Loại quyết định',
            i18n: 'menu.title-loai-quyet-dinh',
            link: '/sys/loai-quyet-dinh',
            acl: ['LOAI_QUYET_DINH_VIEW']
          },
          {
            text: 'Phòng ban',
            i18n: 'menu.title-phong-ban',
            link: '/sys/phong-ban',
            acl: ['PHONG_VIEW']
          },
          {
            text: 'Khối kiến thức',
            i18n: 'menu.title-khoi-kien-thuc',
            link: '/sys/khoi-kien-thuc',
            acl: ['CHUONG_TRINH_DAO_TAO_KIEN_THUC_VIEW']
          },
          {
            text: 'Điểm quy đổi thang 4',
            i18n: 'menu.title-diem-quy-doi-thang-4',
            link: '/sys/diem-quy-doi',
            acl: ['DIEM_QUY_DOI_VIEW']
          },
          {
            text: 'Loại thành phần điểm theo hệ',
            i18n: 'menu.title-loai-thanh-phan-diem-theo-he',
            link: '/sys/thanh-phan-mon-theo-he',
            acl: ['THANH_PHAN_MON_THEO_HE_VIEW']
          },
          {
            text: 'Loại thu chi',
            i18n: 'menu.title-loai-thu-chi',
            link: '/sys/loai-thu-chi',
            acl: ['LOAI_THU_CHI_VIEW']
          },
          {
            text: 'Phòng học',
            i18n: 'menu.title-phong-hoc',
            link: '/sys/phong-hoc',
            acl: ['PHONG_HOC_VIEW']
          },
          {
            text: 'Cấp rèn luyện',
            i18n: 'menu.title-cap-ren-luyen',
            link: '/sys/cap-ren-luyen',
            acl: ['CAP_REN_LUYEN_VIEW']
          },
          {
            text: 'Tầng',
            i18n: 'menu.title-tang',
            link: '/sys/tang',
            acl: ['TANG_VIEW']
          },
          {
            text: 'Loại phòng',
            i18n: 'menu.title-loai-phong',
            link: '/sys/loai-phong',
            acl: ['LOAI_PHONG_VIEW']
          },
          {
            text: 'Chỉ tiêu tuyển sinh',
            i18n: 'menu.title-chi-tieu-tuyen-sinh',
            link: '/sys/chi-tieu-tuyen-sinh',
            acl: ['CHI_TIEU_TUYEN_SINH_VIEW']
          },
          {
            text: 'Bệnh viện',
            i18n: 'menu.title-benh-vien',
            link: '/sys/benh-vien',
            acl: ['BENH_VIEN_VIEW']
          },
          {
            text: 'Phương án',
            i18n: 'menu.title-phuong-an',
            link: '/sys/phuong-an',
            acl: ['PHUONG_AN_VIEW']
          },
          {
            text: 'Phương thức đóng',
            i18n: 'menu.title-phuong-thuc-dong',
            link: '/sys/phuong-thuc-dong',
            acl: ['PHUONG_THUC_DONG_VIEW']
          },
          {
            text: 'Vùng',
            i18n: 'menu.title-vung',
            link: '/sys/vung',
            acl: ['VUNG_VIEW']
          },
          {
            text: 'Mức hưởng BHYT',
            i18n: 'menu.title-muc-huong-bhyt',
            link: '/sys/muc-huong-bhyt',
            acl: ['MUC_HUONG_BHYT_VIEW']
          }
        ]
      }
    ]
  }
];

export const LIST_USER_STATUS = [
  { id: true, code: true, name: 'Đang hoạt động' },
  { id: false, code: false, name: 'Ngừng hoạt động' }
];

export const LIST_STATUS = [
  { id: true, code: true, name: 'Đang áp dụng' },
  { id: false, code: false, name: 'Ngừng áp dụng' }
];

export const LIST_DOI_TUONG_AP_DUNG = [
  { id: 0, name: 'phan_ca_lam_viec.them_moi.radio.can_bo.title' },
  { id: 1, name: 'phan_ca_lam_viec.them_moi.radio.phong_ban.title' }
];

export const DOI_TUONG_AP_DUNG = {
  CANBO: 0,
  DONVI: 1
};

export const DAY_OF_WEEK = [
  { id: 0, key: 'Sunday', value: 'Chủ nhật' },
  { id: 1, key: 'Monday', value: 'Thứ 2' },
  { id: 2, key: 'Tuesday', value: 'Thứ 3' },
  { id: 3, key: 'Wednesday', value: 'Thứ 4' },
  { id: 4, key: 'Thursday', value: 'Thứ 5' },
  { id: 5, key: 'Friday', value: 'Thứ 6' },
  { id: 6, key: 'Saturday', value: 'Thứ 7' }
];

export const DATE_FORMAT = `dd/MM/yyyy`;
export const DATE_FORMAT_yyyyMMddThhmmss = `yyyy-MM-ddThh:mm:ss`;
export const DATE_FORMAT_YYYYMMDDTHHmmss = `YYYY-MM-DDTHH:mm:ss`;
export const DATE_FORMAT_DDMMYYYYHHmmss = `DD/MM/YYYY HH:mm:ss`;
export const DATE_FORMAT_ddMMyyyyHHmmss = `dd/MM/yyyy HH:mm:ss`;
export const DATE_FORMAT_yyyyMMdd = `yyyy-MM-dd`;
export const DATE_FORMAT_YYYYMMDD = `YYYY-MM-DD`;
export const DATE_FORMAT_dd_MM_yyyy = `dd-MM-yyyy`;
export const DATE_FORMAT_ddMMyyyy = `dd/MM/yyyy`;
export const DATE_FORMAT_DDMMyyyy = `DD/MM/yyyy`;
export const LOCALIZE = 'en-US';

export const REGEX_NAME =
  '^[a-zA-Z0-9_ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽếềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳýỵỷỹ -]+';
export const REGEX_CODE = '^[a-zA-Z0-9_-]*$';
export const REGEX_PHONE = '([+]84[3|5|7|8|9]|84[3|5|7|8|9]|0[3|5|7|8|9])+([0-9]{8,10})';
export const REGEX_EMAIL =
  "^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$";
export const REGEX_TIMESPAN = '^(((([0-1][0-9])|(2[0-3])):?[0-5][0-9]:?[0-5][0-9]+$))';
export const QUERY_FILTER_DEFAULT: QueryFilerModel = {
  pageNumber: 1,
  pageSize: 10,
  textSearch: undefined,
  showAdSearch: false
};

export const QUERY_FILTER_MIN_DEFAULT: QueryFilerModel = {
  pageNumber: 1,
  pageSize: 5,
  textSearch: undefined,
  showAdSearch: false
};

export const MAX_WITDH_HIDDEN_LEFT_MENU = 890;

export const FORM_TYPE = {
  ADD: 'add',
  INFO: 'info',
  EDIT: 'edit',
  DEL: 'delete'
};

export const AG_GIRD_CELL_STYLE = { 'border-right': '1px solid #DDE2EB' };

export const EVENT_TYPE = {
  SUCCESS_RELOAD: 'success_reload',
  SUCCESS: 'success',
  CONFIRM: 'confirm',
  CLOSE: 'close'
};

export const LIST_SEX = [
  { value: 1, label: 'Nam' },
  { value: 2, label: 'Nữ' },
  { value: 3, label: 'Không xác định' }
];
export const LIST_DEVICE = [
  { value: 'DEVICE_ANDROID', label: 'Android' },
  { value: 'DEVICE_IOS', label: 'iOS' },
  { value: 'DEVICE_MOBILE', label: 'Mobile' },
  { value: 'DEVICE_WEB', label: 'Web' },
  { value: 'DEVICE_3RD', label: '3rdApp' }
];

export const EMAIL_VALIDATION = '[a-z0-9._%+-]+@[a-z0-9.-]+.[a-z]{2,3}$';

export const PAGE_SIZE_OPTION_DEFAULT = [5, 10, 20, 50];

export const EXCEL_STYLES_DEFAULT = [
  {
    id: 'greenBackground',
    interior: {
      color: '#b5e6b5',
      pattern: 'Solid'
    }
  },
  {
    id: 'redFont',
    font: {
      fontName: 'Calibri Light',
      underline: 'Single',
      italic: true,
      color: '#ff0000'
    }
  },
  {
    id: 'darkGreyBackground',
    interior: {
      color: '#888888',
      pattern: 'Solid'
    },
    font: {
      fontName: 'Calibri Light',
      color: '#ffffff'
    }
  },
  {
    id: 'boldBorders',
    borders: {
      borderBottom: {
        color: '#000000',
        lineStyle: 'Continuous',
        weight: 3
      },
      borderLeft: {
        color: '#000000',
        lineStyle: 'Continuous',
        weight: 3
      },
      borderRight: {
        color: '#000000',
        lineStyle: 'Continuous',
        weight: 3
      },
      borderTop: {
        color: '#000000',
        lineStyle: 'Continuous',
        weight: 3
      }
    }
  },
  {
    id: 'header',
    interior: {
      color: '#CCCCCC',
      pattern: 'Solid'
    },
    alignment: {
      vertical: 'Center',
      horizontal: 'Center'
    },
    font: {
      bold: true,
      fontName: 'Calibri'
    },
    borders: {
      borderBottom: {
        color: '#5687f5',
        lineStyle: 'Continuous',
        weight: 1
      },
      borderLeft: {
        color: '#5687f5',
        lineStyle: 'Continuous',
        weight: 1
      },
      borderRight: {
        color: '#5687f5',
        lineStyle: 'Continuous',
        weight: 1
      },
      borderTop: {
        color: '#5687f5',
        lineStyle: 'Continuous',
        weight: 1
      }
    }
  },
  {
    id: 'dateFormat',
    dataType: 'dateTime',
    numberFormat: { format: 'mm/dd/yyyy;@' }
  },
  {
    id: 'twoDecimalPlaces',
    numberFormat: { format: '#,##0.00' }
  },
  {
    id: 'textFormat',
    dataType: 'string'
  },
  {
    id: 'bigHeader',
    font: { size: 25 }
  }
];

export const OVERLAY_LOADING_TEMPLATE = '<span class="ag-overlay-loading-center">Đang tải dữ liệu, vui lòng chờ!</span>';

export const OVERLAY_NOROW_TEMPLATE =
  '<span style="padding: 10px; border: 2px solid #444; background: lightgoldenrodyellow;">Không có dữ liệu!</span>';

export const TOKEN_KEY = {
  REDIRECT_AFTER_LOGIN_URL: 'redirect_after_login_url'
};

export const TRANG_THAI_THAM_SO_CONSTANTS = [
  { value: true, label: 'Đang sử dụng' },
  { value: false, label: 'Ngừng sử dụng' }
];
