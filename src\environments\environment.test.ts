// This file can be replaced during build by using the `fileReplacements` array.
// `ng build ---prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

import { Environment } from '@delon/theme';

export const environment = {
  serverUrl: `./`,
  build: '000001',
  version: 'dev-1.0.0',
  production: false,
  phanHe: 'UniSystem',
  useHash: false,
  authType: 'sso', // authorizeType support list: sso, hou, jwt, keycloak
  vaultToken: '',
  identiyServer: {
    baseUrl: 'https://moodle.unisoft.edu.vn',
    clientId: 'uni-hrm-portal-client'
  },
  keycloakServer: {
    baseUrl: 'https://keycloak.unisoft.edu.vn',
    realm: 'uni-system',
    clientId: 'uni-system-portal-client',
    scopes: 'openid profile email'
  },
  api: {
    baseUrl: 'https://gateway.unisoft.edu.vn',
    refreshTokenEnabled: true,
    refreshTokenType: 'auth-refresh'
  },
  pro: {
    theme: 'light',
    menu: 'side',
    contentWidth: 'fluid',
    fixedHeader: false,
    autoHideHeader: false,
    fixSiderbar: true,
    onlyIcon: false
  }
} as Environment;

/*
 * In development mode, to ignore zone related error stack frames such as
 * `zone.run`, `zoneDelegate.invokeTask` for easier debugging, you can
 * import the following file, but please comment it out in production mode
 * because it will have performance impact when throw error
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
