# Hướng dẫn Validation User cho KeyCloak vs SSO

## Tổng quan

Hệ thống đã được cập nhật để phân biệt logic validation user giữa SSO và KeyCloak trong `oidc-callback.component.ts`.

## Logic Validation

### 1. SSO Authentication
```typescript
// Với SSO, giữ logic cũ
if (user == null || !user?.isActive) {
  this.showUserIsNotActive();
  return;
}
```

**Kiểm tra:**
- `user` không null
- `user.isActive` phải là true

### 2. KeyCloak Authentication
```typescript
// Với KeyCloak, kiểm tra thuộc tính sub
if (!user?.sub) {
  console.error('KeyCloak authentication error: user.sub is null or undefined');
  this.showUserIsNotActive();
  return;
}
```

**Kiểm tra:**
- `user.sub` phải tồn tại và không null/undefined
- Log error message cụ thể cho KeyCloak

## Cấu trúc User Object

### SSO User Object
```typescript
{
  isActive: boolean,
  // ... other properties
}
```

### KeyCloak User Object
```typescript
{
  sub: string,        // Subject identifier - bắt buộc
  email?: string,
  name?: string,
  preferred_username?: string,
  // ... other OIDC standard claims
}
```

## Implementation Code

```typescript
// Kiểm tra user dựa trên authType
if (authType === 'keycloak') {
  // Với KeyCloak, kiểm tra thuộc tính sub
  if (!user?.sub) {
    console.error('KeyCloak authentication error: user.sub is null or undefined');
    this.showUserIsNotActive();
    return;
  }
} else {
  // Với SSO, giữ logic cũ
  if (user == null || !user?.isActive) {
    this.showUserIsNotActive();
    return;
  }
}
```

## Debugging

### KeyCloak Debug
Khi gặp lỗi với KeyCloak, kiểm tra:
1. Console log: "KeyCloak authentication error: user.sub is null or undefined"
2. User object structure từ KeyCloak
3. KeyCloak client configuration

### SSO Debug
Khi gặp lỗi với SSO, kiểm tra:
1. User object có tồn tại không
2. `user.isActive` property
3. Identity Server configuration

## Lưu ý

- **KeyCloak**: Sử dụng `sub` claim theo chuẩn OIDC
- **SSO**: Sử dụng custom `isActive` property
- **Error Handling**: Cả hai đều gọi `showUserIsNotActive()` nhưng KeyCloak có thêm console.error
- **Flexibility**: Dễ dàng thêm validation rules khác cho từng authType

## Test Cases

### Test KeyCloak
```typescript
// Valid user
const validKeycloakUser = {
  sub: "12345-67890-abcdef",
  email: "<EMAIL>",
  name: "John Doe"
};

// Invalid user (missing sub)
const invalidKeycloakUser = {
  email: "<EMAIL>",
  name: "John Doe"
  // sub: undefined - sẽ fail validation
};
```

### Test SSO
```typescript
// Valid user
const validSSOUser = {
  isActive: true,
  email: "<EMAIL>",
  name: "John Doe"
};

// Invalid user
const invalidSSOUser = {
  isActive: false, // hoặc null/undefined
  email: "<EMAIL>",
  name: "John Doe"
};
```

## Kết luận

Logic validation này đảm bảo:
- **Tương thích**: Mỗi authType có validation phù hợp
- **Rõ ràng**: Error message cụ thể cho từng loại
- **Mở rộng**: Dễ dàng thêm validation cho authType mới
- **Maintainable**: Code dễ đọc và debug
