import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
import { QueryFilerModel } from '@model';
import { tonGiaoRouter } from '@util';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TonGiaoService {
  constructor(private http: _HttpClient) {}

  getFilter(model: QueryFilerModel): Observable<any> {
    return this.http.post(environment.api.baseUrl + tonGiaoRouter.getFilter, model);
  }

  delete(id: any): Observable<any> {
    return this.http.delete(environment.api.baseUrl + tonGiaoRouter.delete + id);
  }
  create(model: any): Observable<any> {
    return this.http.post(environment.api.baseUrl + tonGiaoRouter.create, model);
  }

  update(id: any, model: any): Observable<any> {
    return this.http.put(environment.api.baseUrl + tonGiaoRouter.update + id, model);
  }

  getById(id: string): Observable<any> {
    return this.http.get(environment.api.baseUrl + tonGiaoRouter.getById + id);
  }

  getCombobox(): Observable<any> {
    return this.http.get(environment.api.baseUrl + tonGiaoRouter.getCombobox);
  }
}
