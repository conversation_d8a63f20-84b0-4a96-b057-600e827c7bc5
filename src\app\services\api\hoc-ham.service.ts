import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
import { QueryFilerModel } from '@model';
import { hocHamRouter } from '@util';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class HocHamService {
  constructor(private http: _HttpClient) {}

  getFilter(model: QueryFilerModel): Observable<any> {
    return this.http.post(environment.api.baseUrl + hocHamRouter.getFilter, model);
  }

  delete(id: any): Observable<any> {
    return this.http.delete(environment.api.baseUrl + hocHamRouter.delete + id);
  }
  create(model: any): Observable<any> {
    return this.http.post(environment.api.baseUrl + hocHamRouter.create, model);
  }

  update(id: any, model: any): Observable<any> {
    return this.http.put(environment.api.baseUrl + hocHamRouter.update + id, model);
  }

  getById(id: string): Observable<any> {
    return this.http.get(environment.api.baseUrl + hocHamRouter.getById + id);
  }

  getCombobox(): Observable<any> {
    return this.http.get(environment.api.baseUrl + hocHamRouter.getCombobox);
  }
}
