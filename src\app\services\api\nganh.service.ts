import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
import { QueryFilerModel } from '@model';
import { nganhRouter } from '@util';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NganhService {
  constructor(private http: _HttpClient) {}

  getFilter(model: QueryFilerModel): Observable<any> {
    return this.http.post(environment.api.baseUrl + nganhRouter.getFilter, model);
  }

  delete(id: any): Observable<any> {
    return this.http.delete(environment.api.baseUrl + nganhRouter.delete + id);
  }
  create(model: any): Observable<any> {
    return this.http.post(environment.api.baseUrl + nganhRouter.create, model);
  }

  update(id: any, model: any): Observable<any> {
    return this.http.put(environment.api.baseUrl + nganhRouter.update + id, model);
  }

  getById(id: string): Observable<any> {
    return this.http.get(environment.api.baseUrl + nganhRouter.getById + id);
  }

  getCombobox(): Observable<any> {
    return this.http.get(environment.api.baseUrl + nganhRouter.getCombobox);
  }
}
