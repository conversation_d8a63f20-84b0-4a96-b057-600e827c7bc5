# Hướng dẫn sử dụng KeyCloak Authentication

## Tổng quan

Hệ thống đã đư<PERSON><PERSON> bổ sung thêm phương thức xác thực KeyCloak với OAuth2, **hoàn toàn tích hợp vào SharedAuthService** giống như SSO hiện có. KeyCloak là một giải pháp Identity and Access Management (IAM) mã nguồn mở.

## Các phương thức xác thực được hỗ trợ

- `sso` - Identity Server (hiện tại)
- `jwt` - Đăng nhập trên màn hình login (hiện tại)
- `hou` - Phần mềm xác thực custom của khách hàng (hiện tại)
- `keycloak` - KeyCloak OAuth2 (mới)

## Cấu hình KeyCloak

### 1. <PERSON><PERSON><PERSON> hì<PERSON> trong Environment

Thêm cấu hình KeyCloak vào các file environment:

```typescript
// src/environments/environment.ts
export const environment = {
  // ... các cấu hình khác
  authType: 'keycloak', // Thay đổi từ 'sso' thành 'keycloak'
  keycloakServer: {
    baseUrl: 'https://keycloak.unisoft.edu.vn',
    realm: 'uni-system',
    clientId: 'uni-system-portal-client',
    scopes: 'openid profile email'
  },
  // ... các cấu hình khác
};
```

### 2. Cấu hình trong JSON files

Cập nhật các file JSON environment:

```json
// src/assets/env-keycloak.json
{
  "serverUrl": "./",
  "authType": "keycloak",
  "api": {
    "baseUrl": "http://localhost:8000",
    "refreshTokenEnabled": true
  },
  "keycloakServer": {
    "baseUrl": "http://localhost:8080",
    "realm": "uni-system",
    "clientId": "uni-system-portal-client",
    "scopes": "openid profile email"
  }
}
```

## Cấu hình KeyCloak Server

### 1. Tạo Realm

1. Đăng nhập vào KeyCloak Admin Console
2. Tạo realm mới với tên `uni-system`
3. Cấu hình các settings cơ bản cho realm

### 2. Tạo Client

1. Trong realm `uni-system`, tạo client mới với ID `uni-system-portal-client`
2. Cấu hình client:
   - Client Protocol: `openid-connect`
   - Access Type: `public` hoặc `confidential`
   - Valid Redirect URIs: `http://localhost:4200/oidc-callback`
   - Web Origins: `http://localhost:4200`

### 3. Cấu hình Scopes

Đảm bảo client có quyền truy cập các scopes:
- `openid`
- `profile`
- `email`

## Tích hợp thống nhất với SharedAuthService

KeyCloak authentication được **tích hợp hoàn toàn vào SharedAuthService**, không cần service riêng biệt:

- Sử dụng cùng `oidc-client` library như SSO
- Cùng một service (`SharedAuthService`) xử lý cả SSO và KeyCloak
- Tự động phát hiện `authType` và sử dụng cấu hình tương ứng
- Không cần thêm API endpoints riêng

## Luồng xác thực KeyCloak

### 1. Login Flow

1. User truy cập ứng dụng
2. `SharedAuthService` tự động phát hiện `authType: 'keycloak'`
3. Sử dụng KeyCloak configuration để redirect đến KeyCloak login page
4. User đăng nhập trên KeyCloak
5. KeyCloak redirect về `/oidc-callback` với authorization code
6. `oidc-client` tự động exchange code để lấy access token
7. Lưu token và redirect user vào ứng dụng

### 2. Logout Flow

1. User click logout
2. Hệ thống clear oidc storage (tương tự SSO)
3. `SharedAuthService` tự động sử dụng KeyCloak logout endpoint
4. KeyCloak logout và redirect về trang chủ

## Sử dụng

### 1. Thay đổi authType

Để sử dụng KeyCloak authentication, thay đổi `authType` trong environment:

```typescript
// Trong environment.ts
authType: 'keycloak'
```

### 2. Khởi động ứng dụng

```bash
ng serve
```

### 3. Test authentication

1. Truy cập `http://localhost:4200`
2. Hệ thống sẽ tự động redirect đến KeyCloak login
3. Đăng nhập với tài khoản KeyCloak
4. Sau khi đăng nhập thành công, sẽ được redirect về ứng dụng

## Troubleshooting

### 1. Lỗi redirect URI

Đảm bảo trong KeyCloak client settings:
- Valid Redirect URIs: `http://localhost:4200/oidc-callback`
- Web Origins: `http://localhost:4200`

### 2. Lỗi CORS

Cấu hình Web Origins trong KeyCloak client để cho phép domain của ứng dụng.

### 3. Lỗi oidc-client

Kiểm tra:

- Client ID đúng
- Realm name đúng
- Authority URL đúng format: `https://keycloak-server/realms/realm-name`

## Các file đã được thay đổi

1. `src/app/shared-ui/services/auth.service.ts` - **Tích hợp KeyCloak vào SharedAuthService**
2. `src/environments/environment.ts` - Thêm cấu hình KeyCloak
3. `src/app/shared-ui/routes/oidc-callback/oidc-callback.component.ts` - Hỗ trợ cả SSO và KeyCloak
4. `src/app/shared-ui/routes/logout/logout.component.ts` - Hỗ trợ cả SSO và KeyCloak
5. Các file environment và JSON config files

## Lưu ý

- **KeyCloak được tích hợp hoàn toàn vào SharedAuthService** - không có service riêng
- Hoạt động hoàn toàn tương tự SSO hiện tại
- Sử dụng cùng oidc-client library, chỉ khác Identity Provider
- Có thể dễ dàng chuyển đổi giữa các phương thức xác thực bằng cách thay đổi `authType`
- Không cần thêm API backend mới, sử dụng cùng token validation như SSO
- **Một service duy nhất xử lý cả SSO và KeyCloak** - đơn giản và dễ maintain
