import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { startPageGuard } from '@core';

import { BenhVienComponent } from './catalog/benh-vien/benh-vien/benh-vien.component';
import { BoMonComponent } from './catalog/bo-mon/bo-mon/bo-mon.component';
import { CapKhenThuongKyLuatComponent } from './catalog/cap-khen-thuong-ky-luat/cap-khen-thuong-ky-luat/cap-khen-thuong-ky-luat.component';
import { CapRenLuyenComponent } from './catalog/cap-ren-luyen/cap-ren-luyen/cap-ren-luyen.component';
import { ChiTieuTuyenSinhComponent } from './catalog/chi-tieu-tuyen-sinh/chi-tieu-tuyen-sinh/chi-tieu-tuyen-sinh.component';
import { ChucDanhComponent } from './catalog/chuc-danh/chuc-danh/chuc-danh.component';
import { ChuyenNganhComponent } from './catalog/chuyen-nganh/chuyen-nganh/chuyen-nganh.component';
import { CoSoDaoTaoComponent } from './catalog/co-so-dao-tao/co-so-dao-tao/co-so-dao-tao.component';
import { DanTocComponent } from './catalog/dan-toc/dan-toc/dan-toc.component';
import { DiemQuyDoiComponent } from './catalog/diem-quy-doi/diem-quy-doi/diem-quy-doi.component';
import { DiemRenLuyenQuyDoiComponent } from './catalog/diem-ren-luyen-quy-doi/diem-ren-luyen-quy-doi/diem-ren-luyen-quy-doi.component';
import { DoiTuongHocBongComponent } from './catalog/doi-tuong-hoc-bong/doi-tuong-hoc-bong/doi-tuong-hoc-bong.component';
import { DoiTuongHocPhiComponent } from './catalog/doi-tuong-nop-hoc-phi/doi-tuong-nop-hoc-phi/doi-tuong-hoc-phi.component';
import { DoiTuongComponent } from './catalog/doi-tuong/doi-tuong/doi-tuong.component';
import { DonViThucTapComponent } from './catalog/don-vi-thuc-tap/don-vi-thuc-tap/don-vi-thuc-tap.component';
import { GioiTinhComponent } from './catalog/gioi-tinh/gioi-tinh/gioi-tinh.component';
import { HanhViComponent } from './catalog/hanh-vi/hanh-vi/hanh-vi.component';
import { HeDaoTaoComponent } from './catalog/he-dao-tao/he-dao-tao/he-dao-tao.component';
import { HinhThucHocComponent } from './catalog/hinh-thuc-hoc/hinh-thuc-hoc/hinh-thuc-hoc.component';
import { HinhThucThiComponent } from './catalog/hinh-thuc-thi/hinh-thuc-thi/hinh-thuc-thi.component';
import { HocHamComponent } from './catalog/hoc-ham/hoc-ham/hoc-ham.component';
import { HocKyDangKyComponent } from './catalog/hoc-ky-dang-ky/hoc-ky-dang-ky/hoc-ky-dang-ky.component';
import { HocViComponent } from './catalog/hoc-vi/hoc-vi/hoc-vi.component';
import { HuyenComponent } from './catalog/huyen/huyen/huyen.component';
import { KhoaComponent } from './catalog/khoa/khoa/khoa.component';
import { KhoiKienThucComponent } from './catalog/khoi-kien-thuc/khoi-kien-thuc/khoi-kien-thuc.component';
import { KhuVucComponent } from './catalog/khu-vuc/khu-vuc/khu-vuc.component';
import { LoaiChungChiComponent } from './catalog/loai-chung-chi/loai-chung-chi/loai-chung-chi.component';
import { LoaiGiayToComponent } from './catalog/loai-giay-to/loai-giay-to/loai-giay-to.component';
import { LoaiKhenThuongComponent } from './catalog/loai-khen-thuong/loai-khen-thuong/loai-khen-thuong.component';
import { LoaiPhongComponent } from './catalog/loai-phong/loai-phong/loai-phong.component';
import { LoaiQuyetDinhComponent } from './catalog/loai-quyet-dinh/loai-quyet-dinh/loai-quyet-dinh.component';
import { LoaiRenLuyenComponent } from './catalog/loai-ren-luyen/loai-ren-luyen/loai-ren-luyen.component';
import { LoaiThanhPhanDiemComponent } from './catalog/loai-thanh-phan-diem/loai-thanh-phan-diem/loai-thanh-phan-diem.component';
import { LoaiThuChiComponent } from './catalog/loai-thu-chi/loai-thu-chi/loai-thu-chi.component';
import { MucHuongBhytComponent } from './catalog/muc-huong-bhyt/muc-huong-bhyt/muc-huong-bhyt.component';
import { NganhComponent } from './catalog/nganh/nganh/nganh.component';
import { NhomChungChiComponent } from './catalog/nhom-chung-chi/nhom-chung-chi/nhom-chung-chi.component';
import { NhomDoiTuongComponent } from './catalog/nhom-doi-tuong/nhom-doi-tuong/nhom-doi-tuong.component';
import { PhongBanComponent } from './catalog/phong-ban/phong-ban/phong-ban.component';
import { PhongHocComponent } from './catalog/phong-hoc/phong-hoc/phong-hoc.component';
import { PhuongAnComponent } from './catalog/phuong-an/phuong-an/phuong-an.component';
import { PhuongNgoaiTruComponent } from './catalog/phuong-ngoai-tru/phuong-ngoai-tru/phuong-ngoai-tru.component';
import { PhuongThucDongComponent } from './catalog/phuong-thuc-dong/phuong-thuc-dong/phuong-thuc-dong.component';
import { QuocTichComponent } from './catalog/quoc-tich/quoc-tich/quoc-tich.component';
import { TangComponent } from './catalog/tang/tang/tang.component';
import { ThanhPhanMonTheoHeComponent } from './catalog/thanh-phan-mon-theo-he/thanh-phan-mon-theo-he/thanh-phan-mon-theo-he.component';
import { TinhComponent } from './catalog/tinh/tinh/tinh.component';
import { ToaNhaComponent } from './catalog/toa-nha/toa-nha/toa-nha.component';
import { TonGiaoComponent } from './catalog/ton-giao/ton-giao/ton-giao.component';
import { VungComponent } from './catalog/vung/vung/vung.component';
import { XaComponent } from './catalog/xa/xa/xa.component';
import { XepHangHocLucComponent } from './catalog/xep-hang-hoc-luc/xep-hang-hoc-luc/xep-hang-hoc-luc.component';
import { XepHangNamDaoTaoComponent } from './catalog/xep-hang-nam-dao-tao/xep-hang-nam-dao-tao/xep-hang-nam-dao-tao.component';
import { XepLoaiChungChiComponent } from './catalog/xep-loai-chung-chi/xep-loai-chung-chi/xep-loai-chung-chi.component';
import { XepLoaiHocBongComponent } from './catalog/xep-loai-hoc-bong/xep-loai-hoc-bong/xep-loai-hoc-bong.component';
import { XepLoaiHocLucThang4Component } from './catalog/xep-loai-hoc-luc-thang-4/xep-loai-hoc-luc-thang-4/xep-loai-hoc-luc-thang-4.component';
import { XepLoaiHocTapThangDiem10Component } from './catalog/xep-loai-hoc-tap-thang-10/xep-loai-hoc-tap-thang-10/xep-loai-hoc-tap-thang-10.component';
import { XepLoaiRenLuyenComponent } from './catalog/xep-loai-ren-luyen/xep-loai-ren-luyen/xep-loai-ren-luyen.component';
import { XepLoaiTotNghiepThang10Component } from './catalog/xep-loai-tot-nghiep-thang-10/xep-loai-tot-nghiep-thang-10/xep-loai-tot-nghiep-thang-10.component';
import { XepLoaiTotNghiepThang4Component } from './catalog/xep-loai-tot-nghiep-thang-4/xep-loai-tot-nghiep-thang-4/xep-loai-tot-nghiep-thang-4.component';
import { XuLyComponent } from './catalog/xu-ly/xu-ly/xu-ly.component';
import { EmailLogComponent } from './email-log/email-log.component';
import { EmailTemplateComponent } from './email-template/email-template/email-template.component';
import { ForgotPasswordLogComponent } from './forgot-password-log/forgot-password-log.component';
import { RoleComponent } from './role/role/role.component';
import { SystemLogComponent } from './system-log/system-log.component';
import { UserComponent } from './user/user/user.component';
import { WorkflowItemComponent } from './workflow/workflow-item/workflow-item.component';
import { WorkflowMapFunctionComponent } from './workflow/workflow-map-function/workflow-map-function.component';
import { WorkflowComponent } from './workflow/workflow/workflow.component';

const routes: Routes = [
  {
    path: '',
    // component: LayoutProComponent,
    children: [
      { path: '', redirectTo: 'log', pathMatch: 'full' },
      { path: 'user', component: UserComponent, canActivate: [startPageGuard] },
      { path: 'log', component: SystemLogComponent, canActivate: [startPageGuard] },
      { path: 'email-log', component: EmailLogComponent, canActivate: [startPageGuard] },
      { path: 'forgot-password-log', component: ForgotPasswordLogComponent, canActivate: [startPageGuard] },
      { path: 'role', component: RoleComponent, canActivate: [startPageGuard] },
      { path: 'email-template', component: EmailTemplateComponent, canActivate: [startPageGuard] },
      { path: 'he-dao-tao', component: HeDaoTaoComponent },
      { path: 'ton-giao', component: TonGiaoComponent },
      { path: 'quoc-tich', component: QuocTichComponent },
      { path: 'dan-toc', component: DanTocComponent },
      { path: 'gioi-tinh', component: GioiTinhComponent },
      { path: 'hoc-vi', component: HocViComponent },
      { path: 'hoc-ham', component: HocHamComponent },
      { path: 'khoa', component: KhoaComponent },
      { path: 'nganh', component: NganhComponent },
      { path: 'chuyen-nganh', component: ChuyenNganhComponent },
      { path: 'tinh', component: TinhComponent },
      { path: 'huyen', component: HuyenComponent },
      { path: 'khu-vuc', component: KhuVucComponent },
      { path: 'nhom-doi-tuong', component: NhomDoiTuongComponent },
      { path: 'doi-tuong', component: DoiTuongComponent },
      { path: 'doi-tuong-hoc-bong', component: DoiTuongHocBongComponent },
      { path: 'cap-khen-thuong-ky-luat', component: CapKhenThuongKyLuatComponent },
      { path: 'loai-khen-thuong', component: LoaiKhenThuongComponent },
      { path: 'hanh-vi', component: HanhViComponent },
      { path: 'xu-ly', component: XuLyComponent },
      { path: 'chuc-danh', component: ChucDanhComponent },
      { path: 'xep-loai-ren-luyen', component: XepLoaiRenLuyenComponent },
      { path: 'loai-ren-luyen', component: LoaiRenLuyenComponent },
      { path: 'loai-thanh-phan-diem', component: LoaiThanhPhanDiemComponent },
      { path: 'xep-loai-hoc-tap-thang-10', component: XepLoaiHocTapThangDiem10Component },
      { path: 'xep-hang-hoc-luc', component: XepHangHocLucComponent },
      { path: 'xep-hang-nam-dao-tao', component: XepHangNamDaoTaoComponent },
      { path: 'xep-loai-hoc-tap-thang-4', component: XepLoaiHocLucThang4Component },
      { path: 'xep-loai-tot-nghiep-thang-4', component: XepLoaiTotNghiepThang4Component },
      { path: 'xep-loai-tot-nghiep-thang-10', component: XepLoaiTotNghiepThang10Component },
      { path: 'loai-chung-chi', component: LoaiChungChiComponent },
      { path: 'xep-loai-chung-chi', component: XepLoaiChungChiComponent },
      { path: 'nhom-chung-chi', component: NhomChungChiComponent },
      { path: 'loai-giay-to', component: LoaiGiayToComponent },
      { path: 'xep-loai-hoc-bong', component: XepLoaiHocBongComponent },
      { path: 'hoc-ky-dang-ky', component: HocKyDangKyComponent },
      { path: 'phuong-ngoai-tru', component: PhuongNgoaiTruComponent },
      { path: 'xa', component: XaComponent },
      { path: 'hinh-thuc-hoc', component: HinhThucHocComponent },
      { path: 'hinh-thuc-thi', component: HinhThucThiComponent },
      { path: 'toa-nha', component: ToaNhaComponent },
      { path: 'doi-tuong-hoc-phi', component: DoiTuongHocPhiComponent },
      { path: 'bo-mon', component: BoMonComponent },
      { path: 'co-so-dao-tao', component: CoSoDaoTaoComponent },
      { path: 'diem-ren-luyen-quy-doi', component: DiemRenLuyenQuyDoiComponent },
      { path: 'don-vi-thuc-tap', component: DonViThucTapComponent },
      { path: 'loai-quyet-dinh', component: LoaiQuyetDinhComponent },

      { path: 'phong-ban', component: PhongBanComponent },
      { path: 'khoi-kien-thuc', component: KhoiKienThucComponent },
      { path: 'diem-quy-doi', component: DiemQuyDoiComponent },
      { path: 'thanh-phan-mon-theo-he', component: ThanhPhanMonTheoHeComponent },
      { path: 'loai-thu-chi', component: LoaiThuChiComponent },
      { path: 'phong-hoc', component: PhongHocComponent },
      { path: 'cap-ren-luyen', component: CapRenLuyenComponent },
      { path: 'tang', component: TangComponent },
      { path: 'loai-phong', component: LoaiPhongComponent },
      { path: 'chi-tieu-tuyen-sinh', component: ChiTieuTuyenSinhComponent },
      { path: 'benh-vien', component: BenhVienComponent },
      { path: 'phuong-an', component: PhuongAnComponent },
      { path: 'phuong-thuc-dong', component: PhuongThucDongComponent },
      { path: 'vung', component: VungComponent },
      { path: 'muc-huong-bhyt', component: MucHuongBhytComponent },

      // Workflow
      { path: 'workflow', component: WorkflowComponent },
      { path: 'workflow/designer', component: WorkflowItemComponent },
      { path: 'workflow/map-function', component: WorkflowMapFunctionComponent }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SystemRoutingModule {}
